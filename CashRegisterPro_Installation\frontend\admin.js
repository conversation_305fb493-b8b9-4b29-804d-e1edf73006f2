document.addEventListener('DOMContentLoaded', () => {
  // Check if user is logged in and is admin
  const token = localStorage.getItem('authToken');
  const userRole = localStorage.getItem('userRole');
  const username = localStorage.getItem('username');

  if (!token || userRole !== 'admin') {
    window.location.href = 'index.html';
    return;
  }

  // Set up axios defaults
  axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;

  // DOM elements
  const userInfo = document.getElementById('user-info');
  const userNameElement = document.getElementById('user-name');
  const logoutBtn = document.getElementById('logout-btn');
  const navBtns = document.querySelectorAll('.nav-btn');
  const sections = document.querySelectorAll('.admin-section');
  const sidebarToggle = document.getElementById('sidebar-toggle');
  const sidebar = document.getElementById('admin-sidebar');

  // Initialize
  init();

  // Event listeners
  logoutBtn.addEventListener('click', logout);

  // Sidebar toggle functionality
  if (sidebarToggle && sidebar) {
    sidebarToggle.addEventListener('click', () => {
      sidebar.classList.toggle('collapsed');
    });
  }

  // Window controls
  setupWindowControls();

  // Theme toggle
  setupThemeToggle();

  // Navigation
  navBtns.forEach(btn => {
    btn.addEventListener('click', () => {
      const sectionId = btn.getAttribute('data-section');
      
      // Update active button
      navBtns.forEach(b => b.classList.remove('active'));
      btn.classList.add('active');
      
      // Show selected section
      sections.forEach(section => {
        section.classList.remove('active');
        if (section.id === `${sectionId}-section`) {
          section.classList.add('active');
        }
      });
      
      // Load section data
      loadSectionData(sectionId);
    });
  });
  
  // Products section
  const addProductBtn = document.getElementById('add-product-btn');
  const productModal = document.getElementById('product-modal');
  const productForm = document.getElementById('product-form');
  const productModalTitle = document.getElementById('product-modal-title');
  const productNameInput = document.getElementById('product-name');
  const productCategoryInput = document.getElementById('product-category');
  const productPriceInput = document.getElementById('product-price');
  
  addProductBtn.addEventListener('click', () => {
    productModalTitle.textContent = 'Add Product';
    productForm.reset();
    productForm.setAttribute('data-mode', 'add');
    openModal(productModal);
  });
  
  productForm.addEventListener('submit', async (e) => {
    e.preventDefault();
    
    const mode = productForm.getAttribute('data-mode');
    const productData = {
      name: productNameInput.value,
      category: productCategoryInput.value,
      price: parseFloat(productPriceInput.value)
    };
    
    try {
      if (mode === 'add') {
        await axios.post('http://localhost:5000/api/products', productData);
      } else {
        const productId = productForm.getAttribute('data-id');
        await axios.put(`http://localhost:5000/api/products/${productId}`, productData);
      }
      
      closeModal(productModal);
      loadProducts();
    } catch (error) {
      console.error('Error saving product:', error);
      alert('Failed to save product');
    }
  });
  
  // Tables section
  const addTableBtn = document.getElementById('add-table-btn');
  const tableModal = document.getElementById('table-modal');
  const tableForm = document.getElementById('table-form');
  const tableModalTitle = document.getElementById('table-modal-title');
  const tableNameInput = document.getElementById('table-name');
  const tableStatusInput = document.getElementById('table-status');
  
  addTableBtn.addEventListener('click', () => {
    tableModalTitle.textContent = 'Add Table';
    tableForm.reset();
    tableForm.setAttribute('data-mode', 'add');
    openModal(tableModal);
  });
  
  tableForm.addEventListener('submit', async (e) => {
    e.preventDefault();
    
    const mode = tableForm.getAttribute('data-mode');
    const tableData = {
      name: tableNameInput.value,
      status: tableStatusInput.value
    };
    
    try {
      if (mode === 'add') {
        await axios.post('http://localhost:5000/api/tables', tableData);
      } else {
        const tableId = tableForm.getAttribute('data-id');
        await axios.put(`http://localhost:5000/api/tables/${tableId}`, tableData);
      }
      
      closeModal(tableModal);
      loadTables();
    } catch (error) {
      console.error('Error saving table:', error);
      alert('Failed to save table');
    }
  });
  
  // Orders section
  const orderModal = document.getElementById('order-modal');
  const orderDetails = document.getElementById('order-details');
  const orderStatusInput = document.getElementById('order-status');
  const updateOrderBtn = document.getElementById('update-order-btn');
  const adminPrintReceiptBtn = document.getElementById('admin-print-receipt-btn');
  
  updateOrderBtn.addEventListener('click', async () => {
    const orderId = updateOrderBtn.getAttribute('data-id');
    const status = orderStatusInput.value;
    
    try {
      await axios.put(`http://localhost:5000/api/orders/${orderId}`, { status });
      closeModal(orderModal);
      loadOrders();
    } catch (error) {
      console.error('Error updating order:', error);
      alert('Failed to update order');
    }
  });
  
  adminPrintReceiptBtn.addEventListener('click', async () => {
    const orderId = updateOrderBtn.getAttribute('data-id');
    
    try {
      const response = await axios.get(`http://localhost:5000/api/sales/print-receipt/${orderId}`);
      const receipt = response.data;
      
      // In a real application, you would send this to a printer
      // For now, we'll just show the receipt data
      const receiptWindow = window.open('', '_blank');
      receiptWindow.document.write(`
        <html>
          <head>
            <title>Receipt #${receipt.order_id}</title>
            <style>
              body { font-family: monospace; line-height: 1.5; padding: 20px; }
              .header { text-align: center; margin-bottom: 20px; }
              .item { display: flex; justify-content: space-between; }
              .total { margin-top: 10px; font-weight: bold; border-top: 1px solid #000; padding-top: 10px; }
            </style>
          </head>
          <body>
            <div class="header">
              <h2>Receipt #${receipt.order_id}</h2>
              <p>Table: ${receipt.table}</p>
              <p>Date: ${new Date(receipt.date).toLocaleString()}</p>
            </div>
            <div class="items">
              ${receipt.items.map(item => `
                <div class="item">
                  <span>${item.quantity} x ${item.name}</span>
                  <span>$${(item.subtotal).toFixed(2)}</span>
                </div>
              `).join('')}
            </div>
            <div class="total">
              <div class="item">
                <span>Total:</span>
                <span>$${receipt.total.toFixed(2)}</span>
              </div>
            </div>
            <div class="footer" style="text-align: center; margin-top: 30px;">
              <p>Thank you for your purchase!</p>
            </div>
          </body>
        </html>
      `);
      receiptWindow.document.close();
      receiptWindow.print();
    } catch (error) {
      console.error('Error printing receipt:', error);
      alert('Failed to print receipt');
    }
  });
  
  // Sales section
  const salesDateInput = document.getElementById('sales-date');
  const viewDailySalesBtn = document.getElementById('view-daily-sales-btn');
  const salesStartDateInput = document.getElementById('sales-start-date');
  const salesEndDateInput = document.getElementById('sales-end-date');
  const viewRangeSalesBtn = document.getElementById('view-range-sales-btn');
  const salesReport = document.getElementById('sales-report');
  
  // Set default dates
  const today = new Date();
  salesDateInput.value = formatDate(today);
  salesEndDateInput.value = formatDate(today);
  salesStartDateInput.value = formatDate(new Date(today.setDate(today.getDate() - 7)));
  
  viewDailySalesBtn.addEventListener('click', async () => {
    const date = salesDateInput.value;
    
    try {
      const response = await axios.get(`http://localhost:5000/api/sales/daily?date=${date}`);
      const data = response.data;
      
      salesReport.innerHTML = `
        <div class="sales-summary">
          <div class="sales-date">Date: ${data.date}</div>
          <div class="sales-total">Total Sales: $${data.total_sales.toFixed(2)}</div>
        </div>
        
        <div class="sales-categories">
          <h3>Sales by Category</h3>
          ${data.by_category.map(cat => `
            <div class="sales-category">
              <span>${cat.category}</span>
              <span>$${cat.total.toFixed(2)}</span>
            </div>
          `).join('') || '<p>No category data available</p>'}
        </div>
        
        <div class="sales-products">
          <h3>Top Selling Products</h3>
          ${data.top_products.map(prod => `
            <div class="sales-product">
              <span>${prod.name}</span>
              <span>${prod.quantity} units</span>
            </div>
          `).join('') || '<p>No product data available</p>'}
        </div>
      `;
    } catch (error) {
      console.error('Error loading daily sales:', error);
      alert('Failed to load daily sales');
    }
  });
  
  viewRangeSalesBtn.addEventListener('click', async () => {
    const startDate = salesStartDateInput.value;
    const endDate = salesEndDateInput.value;
    
    try {
      const response = await axios.get(`http://localhost:5000/api/sales/range?start=${startDate}&end=${endDate}`);
      const data = response.data;
      
      salesReport.innerHTML = `
        <div class="sales-summary">
          <div class="sales-date">Period: ${data.start_date} to ${data.end_date}</div>
          <div class="sales-total">Total Sales: $${data.total_sales.toFixed(2)}</div>
        </div>
        
        <div class="sales-chart">
          <h3>Daily Sales</h3>
          <table>
            <thead>
              <tr>
                <th>Date</th>
                <th>Sales</th>
              </tr>
            </thead>
            <tbody>
              ${data.daily_sales.map(day => `
                <tr>
                  <td>${day.date}</td>
                  <td>$${day.total.toFixed(2)}</td>
                </tr>
              `).join('') || '<tr><td colspan="2">No data available</td></tr>'}
            </tbody>
          </table>
        </div>
      `;
    } catch (error) {
      console.error('Error loading sales range:', error);
      alert('Failed to load sales range');
    }
  });
  
  // Cashiers section
  const addCashierBtn = document.getElementById('add-cashier-btn');
  const cashierModal = document.getElementById('cashier-modal');
  const cashierForm = document.getElementById('cashier-form');
  const cashierModalTitle = document.getElementById('cashier-modal-title');
  const cashierUsernameInput = document.getElementById('cashier-username');
  const cashierPasswordInput = document.getElementById('cashier-password');
  const cashierRoleInput = document.getElementById('cashier-role');

  if (addCashierBtn) {
    addCashierBtn.addEventListener('click', () => {
      cashierModalTitle.textContent = 'Add New Cashier';
      cashierForm.reset();
      cashierForm.setAttribute('data-mode', 'add');
      openModal(cashierModal);
    });
  }

  if (cashierForm) {
    cashierForm.addEventListener('submit', async (e) => {
      e.preventDefault();

      const mode = cashierForm.getAttribute('data-mode');
      const cashierData = {
        username: cashierUsernameInput.value,
        password: cashierPasswordInput.value,
        role: cashierRoleInput.value
      };

      try {
        if (mode === 'add') {
          await axios.post('http://localhost:5000/api/auth/register', cashierData);
        } else {
          const cashierId = cashierForm.getAttribute('data-id');
          await axios.put(`http://localhost:5000/api/auth/users/${cashierId}`, cashierData);
        }

        closeModal(cashierModal);
        loadCashiers();
        loadDashboardStats(); // Update stats
      } catch (error) {
        console.error('Error saving cashier:', error);
        alert('Failed to save cashier: ' + (error.response?.data?.error || error.message));
      }
    });
  }

  // Users section (legacy)
  const addUserBtn = document.getElementById('add-user-btn');
  const userModal = document.getElementById('user-modal');
  const userForm = document.getElementById('user-form');
  const userModalTitle = document.getElementById('user-modal-title');
  const userUsernameInput = document.getElementById('user-username');
  const userPasswordInput = document.getElementById('user-password');
  const userRoleInput = document.getElementById('user-role');
  
  addUserBtn.addEventListener('click', () => {
    userModalTitle.textContent = 'Add User';
    userForm.reset();
    userForm.setAttribute('data-mode', 'add');
    openModal(userModal);
  });
  
  userForm.addEventListener('submit', async (e) => {
    e.preventDefault();
    
    const mode = userForm.getAttribute('data-mode');
    const userData = {
      username: userUsernameInput.value,
      password: userPasswordInput.value,
      role: userRoleInput.value
    };
    
    try {
      if (mode === 'add') {
        await axios.post('http://localhost:5000/api/auth/register', userData);
      } else {
        const userId = userForm.getAttribute('data-id');
        await axios.put(`http://localhost:5000/api/auth/users/${userId}`, userData);
      }
      
      closeModal(userModal);
      loadUsers();
    } catch (error) {
      console.error('Error saving user:', error);
      alert('Failed to save user');
    }
  });
  
  // Close modal buttons
  document.querySelectorAll('.close-btn').forEach(btn => {
    btn.addEventListener('click', () => {
      const modal = btn.closest('.modal');
      closeModal(modal);
    });
  });
  
  // Setup window controls
  function setupWindowControls() {
    try {
      const { ipcRenderer } = require('electron');

      const minimizeBtn = document.getElementById('minimize-btn');
      const maximizeBtn = document.getElementById('maximize-btn');
      const closeBtn = document.getElementById('close-btn');

      if (minimizeBtn) {
        minimizeBtn.addEventListener('click', () => {
          ipcRenderer.invoke('window-minimize');
        });
      }

      if (maximizeBtn) {
        maximizeBtn.addEventListener('click', () => {
          ipcRenderer.invoke('window-maximize');
        });
      }

      if (closeBtn) {
        closeBtn.addEventListener('click', () => {
          window.close();
        });
      }
    } catch (error) {
      console.log('Window controls not available (running in browser)');
    }
  }

  // Setup theme toggle
  function setupThemeToggle() {
    const themeToggle = document.getElementById('theme-toggle');
    const themeIcon = themeToggle?.querySelector('.theme-icon');

    if (themeToggle) {
      themeToggle.addEventListener('click', () => {
        const currentTheme = document.documentElement.getAttribute('data-theme');
        const newTheme = currentTheme === 'dark' ? 'light' : 'dark';

        document.documentElement.setAttribute('data-theme', newTheme);
        localStorage.setItem('theme', newTheme);

        if (themeIcon) {
          themeIcon.textContent = newTheme === 'dark' ? '☀️' : '🌙';
        }
      });

      // Load saved theme
      const savedTheme = localStorage.getItem('theme') || 'light';
      document.documentElement.setAttribute('data-theme', savedTheme);
      if (themeIcon) {
        themeIcon.textContent = savedTheme === 'dark' ? '☀️' : '🌙';
      }
    }
  }

  // Functions
  async function init() {
    try {
      // Get user info from token
      const payload = parseJwt(token);
      if (userNameElement) {
        userNameElement.textContent = payload.username || 'Admin';
      }

      // Load dashboard stats
      loadDashboardStats();

      // Load initial section (dashboard)
      loadSectionData('dashboard');
    } catch (error) {
      console.error('Initialization error:', error);
      alert('Failed to initialize dashboard. Please try again.');
    }
  }

  // Load dashboard statistics
  async function loadDashboardStats() {
    try {
      // Load cashiers count
      const usersResponse = await axios.get('http://localhost:5000/api/auth/users');
      const cashiers = usersResponse.data.filter(user => user.role === 'cashier');
      document.getElementById('total-cashiers').textContent = cashiers.length;

      // Load tables count
      const tablesResponse = await axios.get('http://localhost:5000/api/tables');
      document.getElementById('total-tables').textContent = tablesResponse.data.length;

      // Load menu items count
      const productsResponse = await axios.get('http://localhost:5000/api/products');
      document.getElementById('total-menu-items').textContent = productsResponse.data.length;

      // Load today's sales
      const today = formatDate(new Date());
      const salesResponse = await axios.get(`http://localhost:5000/api/sales/daily?date=${today}`);
      document.getElementById('today-sales').textContent = `$${salesResponse.data.total_sales.toFixed(2)}`;

    } catch (error) {
      console.error('Error loading dashboard stats:', error);
    }
  }
  
  function loadSectionData(section) {
    switch (section) {
      case 'dashboard':
        loadDashboardStats();
        break;
      case 'cashiers':
        loadCashiers();
        break;
      case 'tables':
        loadTables();
        break;
      case 'menu':
        loadMenuItems();
        break;
      case 'categories':
        loadCategories();
        break;
      case 'orders':
        loadOrders();
        break;
      case 'sales':
        // Sales data is loaded on demand via buttons
        break;
      case 'inventory':
        loadInventory();
        break;
      case 'settings':
        loadSettings();
        break;
    }
  }

  // Load cashiers (same as users but filtered)
  async function loadCashiers() {
    try {
      const response = await axios.get('http://localhost:5000/api/auth/users');
      const users = response.data;

      const cashiersTable = document.getElementById('cashiers-table');
      if (cashiersTable) {
        cashiersTable.innerHTML = `
          <thead>
            <tr>
              <th>ID</th>
              <th>Username</th>
              <th>Role</th>
              <th>Status</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            ${users.map(user => `
              <tr>
                <td>${user.id}</td>
                <td>${user.username}</td>
                <td>${user.role}</td>
                <td><span class="status-badge status-active">Active</span></td>
                <td>
                  <button class="action-btn edit-btn" data-id="${user.id}">Edit</button>
                  <button class="action-btn delete-btn" data-id="${user.id}">Delete</button>
                </td>
              </tr>
            `).join('')}
          </tbody>
        `;

        // Add event listeners
        cashiersTable.querySelectorAll('.edit-btn').forEach(btn => {
          btn.addEventListener('click', () => editCashier(btn.getAttribute('data-id')));
        });

        cashiersTable.querySelectorAll('.delete-btn').forEach(btn => {
          btn.addEventListener('click', () => deleteCashier(btn.getAttribute('data-id')));
        });
      }
    } catch (error) {
      console.error('Error loading cashiers:', error);
      alert('Failed to load cashiers');
    }
  }

  // Load menu items (same as products)
  async function loadMenuItems() {
    loadProducts(); // Reuse existing function
  }

  // Load categories
  async function loadCategories() {
    try {
      const response = await axios.get('http://localhost:5000/api/categories');
      const categories = response.data || [];

      const categoriesTable = document.getElementById('categories-table');
      if (categoriesTable) {
        categoriesTable.innerHTML = `
          <thead>
            <tr>
              <th>ID</th>
              <th>Name</th>
              <th>Description</th>
              <th>Items Count</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            ${categories.map(category => `
              <tr>
                <td>${category.id}</td>
                <td>${category.name}</td>
                <td>${category.description || 'No description'}</td>
                <td>${category.items_count || 0}</td>
                <td>
                  <button class="action-btn edit-btn" data-id="${category.id}">Edit</button>
                  <button class="action-btn delete-btn" data-id="${category.id}">Delete</button>
                </td>
              </tr>
            `).join('')}
          </tbody>
        `;

        // Add event listeners
        categoriesTable.querySelectorAll('.edit-btn').forEach(btn => {
          btn.addEventListener('click', () => editCategory(btn.getAttribute('data-id')));
        });

        categoriesTable.querySelectorAll('.delete-btn').forEach(btn => {
          btn.addEventListener('click', () => deleteCategory(btn.getAttribute('data-id')));
        });
      }
    } catch (error) {
      console.error('Error loading categories:', error);
      // Create default categories if none exist
      createDefaultCategories();
    }
  }

  // Load inventory
  async function loadInventory() {
    // Placeholder for inventory functionality
    console.log('Inventory section loaded');
  }

  // Load settings
  async function loadSettings() {
    // Placeholder for settings functionality
    console.log('Settings section loaded');
  }
  
  async function loadProducts() {
    try {
      const response = await axios.get('http://localhost:5000/api/products');
      const products = response.data;
      
      const productsTable = document.getElementById('products-table');
      productsTable.innerHTML = `
        <thead>
          <tr>
            <th>ID</th>
            <th>Name</th>
            <th>Category</th>
            <th>Price</th>
            <th>Actions</th>
          </tr>
        </thead>
        <tbody>
          ${products.map(product => `
            <tr>
              <td>${product.id}</td>
              <td>${product.name}</td>
              <td>${product.category}</td>
              <td>$${product.price.toFixed(2)}</td>
              <td>
                <button class="action-btn edit-btn" data-id="${product.id}">Edit</button>
                <button class="action-btn delete-btn" data-id="${product.id}">Delete</button>
              </td>
            </tr>
          `).join('')}
        </tbody>
      `;
      
      // Add event listeners to edit buttons
      productsTable.querySelectorAll('.edit-btn').forEach(btn => {
        btn.addEventListener('click', () => editProduct(btn.getAttribute('data-id')));
      });
      
      // Add event listeners to delete buttons
      productsTable.querySelectorAll('.delete-btn').forEach(btn => {
        btn.addEventListener('click', () => deleteProduct(btn.getAttribute('data-id')));
      });
    } catch (error) {
      console.error('Error loading products:', error);
      alert('Failed to load products');
    }
  }
  
  async function editProduct(productId) {
    try {
      const response = await axios.get(`http://localhost:5000/api/products/${productId}`);
      const product = response.data;
      
      productModalTitle.textContent = 'Edit Product';
      productNameInput.value = product.name;
      productCategoryInput.value = product.category;
      productPriceInput.value = product.price;
      
      productForm.setAttribute('data-mode', 'edit');
      productForm.setAttribute('data-id', productId);
      
      openModal(productModal);
    } catch (error) {
      console.error('Error loading product details:', error);
      alert('Failed to load product details');
    }
  }
  
  async function deleteProduct(productId) {
    if (confirm('Are you sure you want to delete this product?')) {
      try {
        await axios.delete(`http://localhost:5000/api/products/${productId}`);
        loadProducts();
      } catch (error) {
        console.error('Error deleting product:', error);
        alert('Failed to delete product');
      }
    }
  }
  
  async function loadTables() {
    try {
      const response = await axios.get('http://localhost:5000/api/tables');
      const tables = response.data;
      
      const tablesTable = document.getElementById('tables-table');
      tablesTable.innerHTML = `
        <thead>
          <tr>
            <th>ID</th>
            <th>Name</th>
            <th>Status</th>
            <th>Actions</th>
          </tr>
        </thead>
        <tbody>
          ${tables.map(table => `
            <tr>
              <td>${table.id}</td>
              <td>${table.name}</td>
              <td>${capitalizeFirstLetter(table.status)}</td>
              <td>
                <button class="action-btn edit-btn" data-id="${table.id}">Edit</button>
                <button class="action-btn delete-btn" data-id="${table.id}">Delete</button>
              </td>
            </tr>
          `).join('')}
        </tbody>
      `;
      
      // Add event listeners to edit buttons
      tablesTable.querySelectorAll('.edit-btn').forEach(btn => {
        btn.addEventListener('click', () => editTable(btn.getAttribute('data-id')));
      });
      
      // Add event listeners to delete buttons
      tablesTable.querySelectorAll('.delete-btn').forEach(btn => {
        btn.addEventListener('click', () => deleteTable(btn.getAttribute('data-id')));
      });
    } catch (error) {
      console.error('Error loading tables:', error);
      alert('Failed to load tables');
    }
  }
  
  async function editTable(tableId) {
    try {
      const response = await axios.get(`http://localhost:5000/api/tables/${tableId}`);
      const table = response.data;
      
      tableModalTitle.textContent = 'Edit Table';
      tableNameInput.value = table.name;
      tableStatusInput.value = table.status;
      
      tableForm.setAttribute('data-mode', 'edit');
      tableForm.setAttribute('data-id', tableId);
      
      openModal(tableModal);
    } catch (error) {
      console.error('Error loading table details:', error);
      alert('Failed to load table details');
    }
  }
  
  async function deleteTable(tableId) {
    if (confirm('Are you sure you want to delete this table?')) {
      try {
        await axios.delete(`http://localhost:5000/api/tables/${tableId}`);
        loadTables();
      } catch (error) {
        console.error('Error deleting table:', error);
        alert('Failed to delete table');
      }
    }
  }
  
  async function loadOrders() {
    try {
      const response = await axios.get('http://localhost:5000/api/orders');
      const orders = response.data;
      
      const ordersTable = document.getElementById('orders-table');
      ordersTable.innerHTML = `
        <thead>
          <tr>
            <th>ID</th>
            <th>Table</th>
            <th>Date</th>
            <th>Total</th>
            <th>Status</th>
            <th>Actions</th>
          </tr>
        </thead>
        <tbody>
          ${orders.map(order => `
            <tr>
              <td>${order.id}</td>
              <td>${order.table_name}</td>
              <td>${new Date(order.date).toLocaleString()}</td>
              <td>$${order.total.toFixed(2)}</td>
              <td>${capitalizeFirstLetter(order.status)}</td>
              <td>
                <button class="action-btn view-btn" data-id="${order.id}">View</button>
                <button class="action-btn edit-btn" data-id="${order.id}">Update</button>
              </td>
            </tr>
          `).join('')}
        </tbody>
      `;
      
      // Add event listeners to view buttons
      ordersTable.querySelectorAll('.view-btn').forEach(btn => {
        btn.addEventListener('click', () => viewOrder(btn.getAttribute('data-id')));
      });
      
      // Add event listeners to edit buttons
      ordersTable.querySelectorAll('.edit-btn').forEach(btn => {
        btn.addEventListener('click', () => updateOrderStatus(btn.getAttribute('data-id')));
      });
    } catch (error) {
      console.error('Error loading orders:', error);
      alert('Failed to load orders');
    }
  }
  
  async function viewOrder(orderId) {
    try {
      const response = await axios.get(`http://localhost:5000/api/orders/${orderId}`);
      const order = response.data;
      
      const orderDetailsModal = document.getElementById('order-details-modal');
      const orderDetailsContent = document.getElementById('order-details-content');
      
      orderDetailsContent.innerHTML = `
        <h3>Order #${order.id}</h3>
        <p><strong>Table:</strong> ${order.table_name}</p>
        <p><strong>Date:</strong> ${new Date(order.date).toLocaleString()}</p>
        <p><strong>Status:</strong> ${capitalizeFirstLetter(order.status)}</p>
        
        <h4>Items:</h4>
        <table>
          <thead>
            <tr>
              <th>Item</th>
              <th>Quantity</th>
              <th>Price</th>
              <th>Subtotal</th>
            </tr>
          </thead>
          <tbody>
            ${order.items.map(item => `
              <tr>
                <td>${item.name}</td>
                <td>${item.quantity}</td>
                <td>$${item.price.toFixed(2)}</td>
                <td>$${(item.price * item.quantity).toFixed(2)}</td>
              </tr>
            `).join('')}
          </tbody>
          <tfoot>
            <tr>
              <td colspan="3"><strong>Total:</strong></td>
              <td><strong>$${order.total.toFixed(2)}</strong></td>
            </tr>
          </tfoot>
        </table>
      `;
      
      openModal(orderDetailsModal);
    } catch (error) {
      console.error('Error loading order details:', error);
      alert('Failed to load order details');
    }
  }
  
  async function updateOrderStatus(orderId) {
    try {
      const response = await axios.get(`http://localhost:5000/api/orders/${orderId}`);
      const order = response.data;
      
      orderDetails.innerHTML = `
        <p><strong>Order #${order.id}</strong></p>
        <p><strong>Table:</strong> ${order.table_name}</p>
        <p><strong>Date:</strong> ${new Date(order.date).toLocaleString()}</p>
        <p><strong>Total:</strong> $${order.total.toFixed(2)}</p>
      `;
      
      orderStatusInput.value = order.status;
      updateOrderBtn.setAttribute('data-id', orderId);
      adminPrintReceiptBtn.setAttribute('data-id', orderId);
      
      openModal(orderModal);
    } catch (error) {
      console.error('Error loading order details:', error);
      alert('Failed to load order details');
    }
  }
  
  async function loadUsers() {
    try {
      const response = await axios.get('http://localhost:5000/api/auth/users');
      const users = response.data;
      
      const usersTable = document.getElementById('users-table');
      usersTable.innerHTML = `
        <thead>
          <tr>
            <th>ID</th>
            <th>Username</th>
            <th>Role</th>
            <th>Actions</th>
          </tr>
        </thead>
        <tbody>
          ${users.map(user => `
            <tr>
              <td>${user.id}</td>
              <td>${user.username}</td>
              <td>${user.role}</td>
              <td>
                <button class="action-btn edit-btn" data-id="${user.id}">Edit</button>
                <button class="action-btn delete-btn" data-id="${user.id}">Delete</button>
              </td>
            </tr>
          `).join('')}
        </tbody>
      `;
      
      // Add event listeners to edit buttons
      usersTable.querySelectorAll('.edit-btn').forEach(btn => {
        btn.addEventListener('click', () => editUser(btn.getAttribute('data-id')));
      });
      
      // Add event listeners to delete buttons
      usersTable.querySelectorAll('.delete-btn').forEach(btn => {
        btn.addEventListener('click', () => deleteUser(btn.getAttribute('data-id')));
      });
    } catch (error) {
      console.error('Error loading users:', error);
      alert('Failed to load users');
    }
  }
  
  async function editUser(userId) {
    try {
      const response = await axios.get(`http://localhost:5000/api/auth/users/${userId}`);
      const user = response.data;
      
      userModalTitle.textContent = 'Edit User';
      userUsernameInput.value = user.username;
      userPasswordInput.value = ''; // Don't show password
      userRoleInput.value = user.role;
      
      userForm.setAttribute('data-mode', 'edit');
      userForm.setAttribute('data-id', userId);
      
      openModal(userModal);
    } catch (error) {
      console.error('Error loading user details:', error);
      alert('Failed to load user details');
    }
  }
  
  async function deleteUser(userId) {
    if (confirm('Are you sure you want to delete this user?')) {
      try {
        await axios.delete(`http://localhost:5000/api/auth/users/${userId}`);
        loadUsers();
      } catch (error) {
        console.error('Error deleting user:', error);
        alert('Failed to delete user');
      }
    }
  }
  
  function openModal(modal) {
    modal.style.display = 'block';
  }
  
  function closeModal(modal) {
    modal.style.display = 'none';
  }
  
  function logout() {
    localStorage.removeItem('authToken');
    localStorage.removeItem('userRole');
    window.location.href = 'index.html';
  }
  
  // Helper functions
  function parseJwt(token) {
    const base64Url = token.split('.')[1];
    const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
    const jsonPayload = decodeURIComponent(atob(base64).split('').map(c => {
      return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
    }).join(''));
    
    return JSON.parse(jsonPayload);
  }
  
  function formatDate(date) {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  }
  
  // Cashier management functions
  async function editCashier(cashierId) {
    try {
      const response = await axios.get(`http://localhost:5000/api/auth/users/${cashierId}`);
      const cashier = response.data;

      cashierModalTitle.textContent = 'Edit Cashier';
      cashierUsernameInput.value = cashier.username;
      cashierPasswordInput.value = ''; // Don't show password
      cashierRoleInput.value = cashier.role;

      cashierForm.setAttribute('data-mode', 'edit');
      cashierForm.setAttribute('data-id', cashierId);

      openModal(cashierModal);
    } catch (error) {
      console.error('Error loading cashier details:', error);
      alert('Failed to load cashier details');
    }
  }

  async function deleteCashier(cashierId) {
    if (confirm('Are you sure you want to delete this cashier?')) {
      try {
        await axios.delete(`http://localhost:5000/api/auth/users/${cashierId}`);
        loadCashiers();
        loadDashboardStats(); // Update stats
      } catch (error) {
        console.error('Error deleting cashier:', error);
        alert('Failed to delete cashier: ' + (error.response?.data?.error || error.message));
      }
    }
  }

  // Category management functions
  async function editCategory(categoryId) {
    // Placeholder for category editing
    console.log('Edit category:', categoryId);
  }

  async function deleteCategory(categoryId) {
    if (confirm('Are you sure you want to delete this category?')) {
      try {
        await axios.delete(`http://localhost:5000/api/categories/${categoryId}`);
        loadCategories();
      } catch (error) {
        console.error('Error deleting category:', error);
        alert('Failed to delete category');
      }
    }
  }

  async function createDefaultCategories() {
    const defaultCategories = [
      { name: 'Appetizers', description: 'Starter dishes and snacks' },
      { name: 'Main Courses', description: 'Primary dishes and entrees' },
      { name: 'Beverages', description: 'Drinks and refreshments' },
      { name: 'Desserts', description: 'Sweet treats and desserts' }
    ];

    try {
      for (const category of defaultCategories) {
        await axios.post('http://localhost:5000/api/categories', category);
      }
      loadCategories();
    } catch (error) {
      console.error('Error creating default categories:', error);
    }
  }

  // Modal helper functions
  window.closeCashierModal = function() {
    closeModal(cashierModal);
  };

  window.closeTableModal = function() {
    closeModal(document.getElementById('table-modal'));
  };

  window.closeMenuModal = function() {
    closeModal(document.getElementById('menu-modal'));
  };

  window.closeCategoryModal = function() {
    closeModal(document.getElementById('category-modal'));
  };

  function capitalizeFirstLetter(string) {
    return string.charAt(0).toUpperCase() + string.slice(1);
  }
});
