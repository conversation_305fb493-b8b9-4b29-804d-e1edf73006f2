
import os
import sys
import subprocess
import time
import webbrowser
from pathlib import Path

def main():
    print("=" * 60)
    print("💰 CASH REGISTER PRO 💰")
    print("=" * 60)
    print()
    
    # Get installation directory
    install_dir = Path("C:/Program Files/Cash Register Pro")
    if not install_dir.exists():
        install_dir = Path(__file__).parent
    
    backend_dir = install_dir / "backend"
    frontend_dir = install_dir / "frontend"
    
    print("🚀 Starting Cash Register Pro...")
    
    # Start backend
    backend_process = subprocess.Popen([
        sys.executable, "app.py"
    ], cwd=backend_dir)
    
    print("⏳ Waiting for backend to start...")
    time.sleep(3)
    
    # Check if Node.js is available for frontend
    try:
        subprocess.run(['node', '--version'], capture_output=True, check=True)
        print("🖥️  Starting Electron frontend...")
        subprocess.Popen(['npm', 'start'], cwd=frontend_dir)
    except:
        print("🌐 Opening web interface...")
        time.sleep(2)
        webbrowser.open('http://localhost:5000')
    
    print("\n" + "=" * 60)
    print("🎉 CASH REGISTER PRO IS NOW RUNNING! 🎉")
    print("=" * 60)
    print()
    print("📍 Backend: http://localhost:5000")
    print("🖥️  Frontend: Desktop Application")
    print()
    print("🔐 Login Credentials:")
    print("   Admin: admin / admin123")
    print("   Cashier: cashier / cashier123")
    print()
    print("=" * 60)
    
    try:
        backend_process.wait()
    except KeyboardInterrupt:
        print("\n🔄 Shutting down...")
        backend_process.terminate()

if __name__ == "__main__":
    main()
