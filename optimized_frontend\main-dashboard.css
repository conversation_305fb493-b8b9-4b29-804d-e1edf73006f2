/* Main Dashboard Styles for Cash Register Pro */

/* Main Dashboard Container */
.main-dashboard-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: var(--bg-secondary);
  overflow: hidden;
}

/* Main Header */
.main-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-lg) var(--spacing-xl);
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
  color: white;
  box-shadow: var(--shadow-lg);
  z-index: 100;
  position: relative;
}

.header-center {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2px;
}

.current-time {
  font-size: 1.5rem;
  font-weight: 700;
  color: white;
}

.current-date {
  font-size: 0.9rem;
  opacity: 0.9;
  font-weight: 500;
}

.admin-btn {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm) var(--spacing-md);
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: var(--radius-md);
  color: white;
  font-weight: 500;
  transition: all var(--transition-fast);
  backdrop-filter: blur(10px);
}

.admin-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-1px);
}

/* Main Content Layout */
.main-content {
  display: flex;
  flex: 1;
  overflow: hidden;
}

/* Main Sidebar */
.main-sidebar {
  width: 280px;
  background: var(--bg-primary);
  border-right: 1px solid var(--border-light);
  display: flex;
  flex-direction: column;
  transition: width var(--transition-normal);
  position: relative;
  z-index: 50;
  box-shadow: var(--shadow-md);
}

.main-sidebar.collapsed {
  width: 70px;
}

/* Main Area */
.main-area {
  flex: 1;
  padding: var(--spacing-xl);
  overflow-y: auto;
  background: var(--bg-secondary);
}

/* Main Sections */
.main-section {
  display: none;
  animation: fadeIn 0.3s ease-in-out;
}

.main-section.active {
  display: block;
}

/* Table Filters */
.table-filters {
  display: flex;
  gap: var(--spacing-md);
  margin-top: var(--spacing-md);
}

.filter-btn {
  padding: var(--spacing-sm) var(--spacing-md);
  background: var(--bg-primary);
  border: 1px solid var(--border-medium);
  border-radius: var(--radius-md);
  color: var(--text-secondary);
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition-fast);
}

.filter-btn:hover,
.filter-btn.active {
  background: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

/* Tables Grid */
.tables-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: var(--spacing-lg);
  margin-top: var(--spacing-xl);
}

.table-card {
  background: var(--bg-primary);
  border-radius: var(--radius-lg);
  padding: var(--spacing-xl);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--border-light);
  cursor: pointer;
  transition: all var(--transition-fast);
  position: relative;
  overflow: hidden;
}

.table-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
}

.table-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
}

.table-card.occupied::before {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
}

.table-card.reserved::before {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
}

.table-card.maintenance::before {
  background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--spacing-md);
}

.table-number {
  font-size: 2rem;
  font-weight: 800;
  color: var(--text-primary);
}

.table-status {
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-sm);
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.table-status.available {
  background: #dcfce7;
  color: #166534;
}

.table-status.occupied {
  background: #fee2e2;
  color: #991b1b;
}

.table-status.reserved {
  background: #fef3c7;
  color: #92400e;
}

.table-status.maintenance {
  background: #f3f4f6;
  color: #374151;
}

.table-info {
  margin-bottom: var(--spacing-lg);
}

.table-name {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
}

.table-details {
  display: flex;
  gap: var(--spacing-lg);
  font-size: 0.9rem;
  color: var(--text-secondary);
}

.table-capacity,
.table-location {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.table-actions {
  display: flex;
  gap: var(--spacing-sm);
}

.table-action-btn {
  flex: 1;
  padding: var(--spacing-sm) var(--spacing-md);
  border: none;
  border-radius: var(--radius-md);
  font-weight: 600;
  cursor: pointer;
  transition: all var(--transition-fast);
}

.table-action-btn.primary {
  background: var(--primary-color);
  color: white;
}

.table-action-btn.primary:hover {
  background: var(--primary-hover);
  transform: translateY(-1px);
}

.table-action-btn.secondary {
  background: var(--bg-secondary);
  color: var(--text-secondary);
  border: 1px solid var(--border-medium);
}

.table-action-btn.secondary:hover {
  background: var(--bg-tertiary);
  color: var(--text-primary);
}

/* Add Forms */
.add-form-container {
  max-width: 600px;
  margin: 0 auto;
}

.add-form {
  background: var(--bg-primary);
  padding: var(--spacing-2xl);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--border-light);
}

/* Large Modal for Table Orders */
.large-modal {
  max-width: 1200px;
  width: 95%;
  max-height: 90vh;
  overflow: hidden;
}

.order-layout {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: var(--spacing-xl);
  height: 70vh;
}

.menu-selection {
  border-right: 1px solid var(--border-light);
  padding-right: var(--spacing-xl);
  overflow-y: auto;
}

.menu-categories-filter {
  display: flex;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-lg);
  flex-wrap: wrap;
}

.category-filter-btn {
  padding: var(--spacing-sm) var(--spacing-md);
  background: var(--bg-secondary);
  border: 1px solid var(--border-medium);
  border-radius: var(--radius-md);
  color: var(--text-secondary);
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition-fast);
}

.category-filter-btn:hover,
.category-filter-btn.active {
  background: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

.menu-items-list {
  display: grid;
  gap: var(--spacing-md);
}

.menu-item-card {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-md);
  background: var(--bg-secondary);
  border-radius: var(--radius-md);
  border: 1px solid var(--border-light);
  cursor: pointer;
  transition: all var(--transition-fast);
}

.menu-item-card:hover {
  background: var(--bg-tertiary);
  transform: translateX(4px);
}

.menu-item-info h5 {
  margin: 0 0 var(--spacing-xs) 0;
  font-weight: 600;
  color: var(--text-primary);
}

.menu-item-info p {
  margin: 0;
  font-size: 0.9rem;
  color: var(--text-secondary);
}

.menu-item-price {
  font-size: 1.1rem;
  font-weight: 700;
  color: var(--primary-color);
}

/* Order Summary */
.order-summary {
  background: var(--bg-tertiary);
  padding: var(--spacing-lg);
  border-radius: var(--radius-md);
  height: fit-content;
}

.order-items {
  max-height: 300px;
  overflow-y: auto;
  margin-bottom: var(--spacing-lg);
}

.order-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-sm) 0;
  border-bottom: 1px solid var(--border-light);
}

.order-item:last-child {
  border-bottom: none;
}

.order-item-info {
  flex: 1;
}

.order-item-name {
  font-weight: 600;
  color: var(--text-primary);
}

.order-item-controls {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.quantity-btn {
  width: 30px;
  height: 30px;
  border: none;
  border-radius: 50%;
  background: var(--primary-color);
  color: white;
  font-weight: 600;
  cursor: pointer;
  transition: all var(--transition-fast);
}

.quantity-btn:hover {
  background: var(--primary-hover);
  transform: scale(1.1);
}

.quantity-display {
  min-width: 30px;
  text-align: center;
  font-weight: 600;
}

.order-total {
  border-top: 2px solid var(--border-medium);
  padding-top: var(--spacing-md);
  margin-bottom: var(--spacing-lg);
}

.total-line {
  display: flex;
  justify-content: space-between;
  margin-bottom: var(--spacing-sm);
}

.total-final {
  font-size: 1.2rem;
  font-weight: 700;
  color: var(--text-primary);
  border-top: 1px solid var(--border-medium);
  padding-top: var(--spacing-sm);
  margin-top: var(--spacing-sm);
}

.order-actions {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.btn-danger {
  background: #ef4444;
  color: white;
  border: none;
  padding: var(--spacing-md) var(--spacing-lg);
  border-radius: var(--radius-md);
  font-weight: 600;
  cursor: pointer;
  transition: all var(--transition-fast);
}

.btn-danger:hover {
  background: #dc2626;
  transform: translateY(-1px);
}

/* Responsive Design */
@media (max-width: 1024px) {
  .main-sidebar {
    width: 70px;
  }
  
  .main-sidebar .nav-text,
  .main-sidebar .nav-section-title {
    display: none;
  }
  
  .tables-grid {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  }
  
  .order-layout {
    grid-template-columns: 1fr;
    gap: var(--spacing-lg);
  }
  
  .menu-selection {
    border-right: none;
    border-bottom: 1px solid var(--border-light);
    padding-right: 0;
    padding-bottom: var(--spacing-lg);
  }
}

@media (max-width: 768px) {
  .main-content {
    flex-direction: column;
  }
  
  .main-sidebar {
    width: 100%;
    height: auto;
    flex-direction: row;
    overflow-x: auto;
  }
  
  .sidebar-nav {
    display: flex;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
  }
  
  .nav-section {
    margin: 0;
  }
  
  .nav-btn {
    white-space: nowrap;
    min-width: 120px;
  }
  
  .main-area {
    padding: var(--spacing-md);
  }
  
  .tables-grid {
    grid-template-columns: 1fr;
  }
  
  .table-details {
    flex-direction: column;
    gap: var(--spacing-sm);
  }
  
  .large-modal {
    width: 98%;
    max-height: 95vh;
  }
  
  .order-layout {
    height: auto;
    max-height: 80vh;
  }
}
