<!DOCTYPE html>
<html lang="en" data-theme="light">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Cash Register Pro - Main Dashboard</title>
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
  <link rel="stylesheet" href="styles.css">
  <link rel="stylesheet" href="dashboard.css">
  <link rel="stylesheet" href="main-dashboard.css">
</head>
<body>
  <!-- Window Controls -->
  <div class="window-controls">
    <button class="window-control-btn minimize" id="minimize-btn" title="Minimize">−</button>
    <button class="window-control-btn maximize" id="maximize-btn" title="Maximize">□</button>
    <button class="window-control-btn close" id="close-btn" title="Close">×</button>
  </div>

  <!-- Theme Toggle -->
  <button class="theme-toggle" id="theme-toggle" title="Toggle Theme">
    <span class="theme-icon">🌙</span>
  </button>

  <div class="main-dashboard-container">
    <!-- Enhanced Header -->
    <header class="main-header">
      <div class="header-left">
        <div class="logo-section">
          <div class="logo-icon">💰</div>
          <div class="header-title">
            <h1>Cash Register Pro</h1>
            <span class="header-subtitle">Table Management</span>
          </div>
        </div>
      </div>
      
      <div class="header-center">
        <div class="current-time" id="current-time">12:00 PM</div>
        <div class="current-date" id="current-date">Today</div>
      </div>
      
      <div class="header-right">
        <div class="user-info" id="user-info">
          <div class="user-avatar">👤</div>
          <div class="user-details">
            <span class="user-name" id="user-name">Cashier</span>
            <span class="user-role" id="user-role">Cashier</span>
          </div>
        </div>
        <button class="admin-btn" id="admin-btn" title="Admin Panel" style="display: none;">
          <span>⚙️</span>
          <span>Admin</span>
        </button>
        <button class="logout-btn" id="logout-btn" title="Logout">
          <span>🚪</span>
          <span>Logout</span>
        </button>
      </div>
    </header>
    
    <div class="main-content">
      <!-- Sidebar Navigation -->
      <div class="main-sidebar" id="main-sidebar">
        <div class="sidebar-toggle" id="sidebar-toggle">
          <span class="toggle-icon">☰</span>
        </div>
        
        <nav class="sidebar-nav">
          <div class="nav-section">
            <h3 class="nav-section-title">Operations</h3>
            <button class="nav-btn active" data-section="tables">
              <span class="nav-icon">🪑</span>
              <span class="nav-text">Tables</span>
            </button>
            <button class="nav-btn" data-section="orders">
              <span class="nav-icon">📋</span>
              <span class="nav-text">Orders</span>
            </button>
            <button class="nav-btn" data-section="menu">
              <span class="nav-icon">🍽️</span>
              <span class="nav-text">Menu</span>
            </button>
          </div>
          
          <div class="nav-section" id="admin-nav-section" style="display: none;">
            <h3 class="nav-section-title">Management</h3>
            <button class="nav-btn" data-section="add-table">
              <span class="nav-icon">➕</span>
              <span class="nav-text">Add Table</span>
            </button>
            <button class="nav-btn" data-section="add-menu">
              <span class="nav-icon">🍽️➕</span>
              <span class="nav-text">Add Menu Item</span>
            </button>
          </div>
        </nav>
      </div>
      
      <div class="main-area">
        <!-- Tables Section -->
        <div class="main-section active" id="tables-section">
          <div class="section-header">
            <h2>🪑 Restaurant Tables</h2>
            <p class="section-description">Select a table to start taking orders</p>
            <div class="table-filters">
              <button class="filter-btn active" data-filter="all">All Tables</button>
              <button class="filter-btn" data-filter="available">Available</button>
              <button class="filter-btn" data-filter="occupied">Occupied</button>
              <button class="filter-btn" data-filter="reserved">Reserved</button>
            </div>
          </div>
          
          <div class="tables-grid" id="tables-grid">
            <!-- Table cards will be loaded here -->
          </div>
        </div>
        
        <!-- Orders Section -->
        <div class="main-section" id="orders-section">
          <div class="section-header">
            <h2>📋 Active Orders</h2>
            <p class="section-description">Manage current orders and payments</p>
          </div>
          
          <div class="orders-container" id="orders-container">
            <!-- Orders will be loaded here -->
          </div>
        </div>
        
        <!-- Menu Section -->
        <div class="main-section" id="menu-section">
          <div class="section-header">
            <h2>🍽️ Menu Items</h2>
            <p class="section-description">Browse available menu items</p>
          </div>
          
          <div class="menu-categories" id="menu-categories">
            <!-- Menu categories will be loaded here -->
          </div>
          
          <div class="menu-items" id="menu-items">
            <!-- Menu items will be loaded here -->
          </div>
        </div>
        
        <!-- Add Table Section (Admin Only) -->
        <div class="main-section" id="add-table-section">
          <div class="section-header">
            <h2>➕ Add New Table</h2>
            <p class="section-description">Create a new table for the restaurant</p>
          </div>
          
          <div class="add-form-container">
            <form id="add-table-form" class="add-form">
              <div class="form-group">
                <label for="new-table-number">Table Number</label>
                <input type="number" id="new-table-number" placeholder="Enter table number" required min="1">
              </div>
              <div class="form-group">
                <label for="new-table-name">Table Name</label>
                <input type="text" id="new-table-name" placeholder="e.g., Window Table, VIP Table" required>
              </div>
              <div class="form-group">
                <label for="new-table-capacity">Capacity</label>
                <input type="number" id="new-table-capacity" placeholder="Number of seats" required min="1" max="20">
              </div>
              <div class="form-group">
                <label for="new-table-location">Location</label>
                <select id="new-table-location" required>
                  <option value="">Select location</option>
                  <option value="indoor">Indoor</option>
                  <option value="outdoor">Outdoor</option>
                  <option value="patio">Patio</option>
                  <option value="private">Private Room</option>
                  <option value="bar">Bar Area</option>
                </select>
              </div>
              <div class="form-actions">
                <button type="submit" class="btn-primary">Add Table</button>
                <button type="button" class="btn-secondary" onclick="clearTableForm()">Clear</button>
              </div>
            </form>
          </div>
        </div>
        
        <!-- Add Menu Item Section (Admin Only) -->
        <div class="main-section" id="add-menu-section">
          <div class="section-header">
            <h2>🍽️➕ Add Menu Item</h2>
            <p class="section-description">Add a new item to the menu</p>
          </div>
          
          <div class="add-form-container">
            <form id="add-menu-form" class="add-form">
              <div class="form-group">
                <label for="new-menu-name">Item Name</label>
                <input type="text" id="new-menu-name" placeholder="Enter item name" required>
              </div>
              <div class="form-group">
                <label for="new-menu-description">Description</label>
                <textarea id="new-menu-description" placeholder="Describe the item" rows="3"></textarea>
              </div>
              <div class="form-group">
                <label for="new-menu-category">Category</label>
                <select id="new-menu-category" required>
                  <option value="">Select category</option>
                  <!-- Categories will be loaded dynamically -->
                </select>
              </div>
              <div class="form-group">
                <label for="new-menu-price">Price ($)</label>
                <input type="number" id="new-menu-price" placeholder="0.00" step="0.01" min="0" required>
              </div>
              <div class="form-group">
                <label class="checkbox-label">
                  <input type="checkbox" id="new-menu-available" checked>
                  <span class="checkmark"></span>
                  Available for ordering
                </label>
              </div>
              <div class="form-actions">
                <button type="submit" class="btn-primary">Add Menu Item</button>
                <button type="button" class="btn-secondary" onclick="clearMenuForm()">Clear</button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Table Order Modal -->
  <div class="modal" id="table-order-modal">
    <div class="modal-content large-modal">
      <div class="modal-header">
        <h3 id="table-order-title">Table 1 - Order</h3>
        <span class="close-btn" onclick="closeTableOrderModal()">&times;</span>
      </div>
      <div class="modal-body">
        <div class="order-layout">
          <div class="menu-selection">
            <h4>Menu Items</h4>
            <div class="menu-categories-filter" id="modal-menu-categories">
              <!-- Categories will be loaded here -->
            </div>
            <div class="menu-items-list" id="modal-menu-items">
              <!-- Menu items will be loaded here -->
            </div>
          </div>
          <div class="order-summary">
            <h4>Order Summary</h4>
            <div class="order-items" id="order-items">
              <!-- Order items will be added here -->
            </div>
            <div class="order-total">
              <div class="total-line">
                <span>Subtotal:</span>
                <span id="order-subtotal">$0.00</span>
              </div>
              <div class="total-line">
                <span>Tax (8.25%):</span>
                <span id="order-tax">$0.00</span>
              </div>
              <div class="total-line total-final">
                <span>Total:</span>
                <span id="order-total">$0.00</span>
              </div>
            </div>
            <div class="order-actions">
              <button class="btn-primary" onclick="processOrder()">Process Order</button>
              <button class="btn-secondary" onclick="saveOrder()">Save Order</button>
              <button class="btn-danger" onclick="clearOrder()">Clear Order</button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
  <script src="main-dashboard.js"></script>
</body>
</html>
