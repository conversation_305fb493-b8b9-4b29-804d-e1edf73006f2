#!/usr/bin/env python3
"""
Test the optimized Cash Register Pro installation
"""

import os
import sys
import subprocess
import time
import requests
from pathlib import Path

def test_installation():
    print("=" * 60)
    print("🧪 TESTING CASH REGISTER PRO INSTALLATION")
    print("=" * 60)
    print()
    
    # Test optimized backend
    print("🔧 Testing optimized backend...")
    
    backend_dir = Path("CashRegisterPro_Installation/backend")
    if not backend_dir.exists():
        print("❌ Backend directory not found!")
        return False
    
    # Start backend
    backend_process = subprocess.Popen([
        sys.executable, "app.py"
    ], cwd=backend_dir, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
    
    print("⏳ Waiting for backend to start...")
    time.sleep(5)
    
    # Test API endpoints
    try:
        # Test home endpoint
        response = requests.get("http://localhost:5000/", timeout=5)
        if response.status_code == 200:
            print("✅ Backend API responding")
        else:
            print("❌ Backend API not responding properly")
            return False
        
        # Test login endpoint
        login_data = {"username": "admin", "password": "admin123"}
        response = requests.post("http://localhost:5000/api/auth/login", 
                               json=login_data, timeout=5)
        if response.status_code == 200:
            print("✅ Authentication working")
            token = response.json().get('token')
        else:
            print("❌ Authentication failed")
            return False
        
        # Test protected endpoint
        headers = {"Authorization": f"Bearer {token}"}
        response = requests.get("http://localhost:5000/api/auth/verify", 
                              headers=headers, timeout=5)
        if response.status_code == 200:
            print("✅ Token verification working")
        else:
            print("❌ Token verification failed")
            return False
        
        # Test categories endpoint
        response = requests.get("http://localhost:5000/api/categories", timeout=5)
        if response.status_code == 200:
            categories = response.json()
            print(f"✅ Categories loaded: {len(categories)} categories")
        else:
            print("❌ Categories endpoint failed")
            return False
        
        # Test products endpoint
        response = requests.get("http://localhost:5000/api/products", timeout=5)
        if response.status_code == 200:
            products = response.json()
            print(f"✅ Products loaded: {len(products)} products")
        else:
            print("❌ Products endpoint failed")
            return False
        
        # Test tables endpoint
        response = requests.get("http://localhost:5000/api/tables", timeout=5)
        if response.status_code == 200:
            tables = response.json()
            print(f"✅ Tables loaded: {len(tables)} tables")
        else:
            print("❌ Tables endpoint failed")
            return False
        
        # Test daily sales endpoint
        response = requests.get("http://localhost:5000/api/sales/daily", timeout=5)
        if response.status_code == 200:
            sales = response.json()
            print(f"✅ Sales reporting working")
        else:
            print("❌ Sales endpoint failed")
            return False
        
    except requests.exceptions.RequestException as e:
        print(f"❌ API test failed: {e}")
        return False
    
    finally:
        # Clean up
        backend_process.terminate()
        try:
            backend_process.wait(timeout=5)
        except:
            backend_process.kill()
    
    print()
    print("🎨 Testing frontend files...")
    
    frontend_dir = Path("CashRegisterPro_Installation/frontend")
    if not frontend_dir.exists():
        print("❌ Frontend directory not found!")
        return False
    
    # Check essential files
    essential_files = [
        "index.html",
        "main-dashboard.html",
        "admin-dashboard.html",
        "main-dashboard.js",
        "admin.js",
        "renderer.js",
        "main.js",
        "styles.css",
        "package.json"
    ]
    
    for file in essential_files:
        file_path = frontend_dir / file
        if file_path.exists():
            print(f"✅ {file}")
        else:
            print(f"❌ {file} missing")
            return False
    
    # Check assets
    assets_dir = frontend_dir / "assets"
    if assets_dir.exists():
        print("✅ Assets directory")
    else:
        print("❌ Assets directory missing")
        return False
    
    print()
    print("📦 Testing installation package...")
    
    # Check installation files
    install_dir = Path("CashRegisterPro_Installation")
    install_files = [
        "install.bat",
        "launcher.py",
        "README.txt"
    ]
    
    for file in install_files:
        file_path = install_dir / file
        if file_path.exists():
            print(f"✅ {file}")
        else:
            print(f"❌ {file} missing")
            return False
    
    # Check ZIP package
    zip_path = Path("CashRegisterPro_Windows_Installer.zip")
    if zip_path.exists():
        print("✅ ZIP installation package")
        print(f"   Size: {zip_path.stat().st_size / 1024 / 1024:.1f} MB")
    else:
        print("❌ ZIP package missing")
        return False
    
    print()
    print("=" * 60)
    print("🎉 ALL TESTS PASSED SUCCESSFULLY! 🎉")
    print("=" * 60)
    print()
    print("✅ Optimized backend working perfectly")
    print("✅ All API endpoints functional")
    print("✅ Authentication and authorization working")
    print("✅ Database with sample data initialized")
    print("✅ Frontend files optimized and present")
    print("✅ Installation package complete")
    print("✅ ZIP package ready for distribution")
    print()
    print("🚀 The Cash Register Pro installation is ready!")
    print("📦 Distribute: CashRegisterPro_Windows_Installer.zip")
    print()
    print("=" * 60)
    
    return True

if __name__ == "__main__":
    success = test_installation()
    
    if success:
        print("\n🎉 Installation test completed successfully!")
    else:
        print("\n❌ Installation test failed!")
    
    input("Press Enter to exit...")
