document.addEventListener('DOMContentLoaded', () => {
  // Check if user is logged in
  const token = localStorage.getItem('authToken');
  const userRole = localStorage.getItem('userRole');
  const username = localStorage.getItem('username');
  
  if (!token) {
    window.location.href = 'index.html';
    return;
  }
  
  // Set up axios defaults
  axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;
  
  // DOM elements
  const userNameElement = document.getElementById('user-name');
  const userRoleElement = document.getElementById('user-role');
  const logoutBtn = document.getElementById('logout-btn');
  const adminBtn = document.getElementById('admin-btn');
  const adminNavSection = document.getElementById('admin-nav-section');
  const navBtns = document.querySelectorAll('.nav-btn');
  const sections = document.querySelectorAll('.main-section');
  const sidebarToggle = document.getElementById('sidebar-toggle');
  const sidebar = document.getElementById('main-sidebar');
  
  // Current order state
  let currentOrder = {
    tableId: null,
    tableName: '',
    items: [],
    subtotal: 0,
    tax: 0,
    total: 0
  };
  
  // Initialize
  init();
  
  // Event listeners
  logoutBtn.addEventListener('click', logout);
  
  if (adminBtn) {
    adminBtn.addEventListener('click', () => {
      window.location.href = 'admin-dashboard.html';
    });
  }
  
  // Sidebar toggle functionality
  if (sidebarToggle && sidebar) {
    sidebarToggle.addEventListener('click', () => {
      sidebar.classList.toggle('collapsed');
    });
  }
  
  // Navigation
  navBtns.forEach(btn => {
    btn.addEventListener('click', () => {
      const section = btn.getAttribute('data-section');
      showSection(section);
      
      // Update active nav button
      navBtns.forEach(b => b.classList.remove('active'));
      btn.classList.add('active');
    });
  });
  
  // Window controls
  setupWindowControls();
  
  // Theme toggle
  setupThemeToggle();
  
  // Time update
  updateTime();
  setInterval(updateTime, 1000);
  
  // Form submissions
  setupFormHandlers();
  
  // Functions
  async function init() {
    try {
      // Start real-time clock
      startRealTimeClock();

      // Set user info
      if (userNameElement) {
        userNameElement.textContent = username || 'User';
      }
      if (userRoleElement) {
        userRoleElement.textContent = userRole || 'Cashier';
      }

      // Show admin features if user is admin
      if (userRole === 'admin') {
        if (adminBtn) adminBtn.style.display = 'flex';
        if (adminNavSection) adminNavSection.style.display = 'block';
      }

      // Load initial data
      loadTables();
      loadCategories();

    } catch (error) {
      console.error('Initialization error:', error);
      alert('Failed to initialize dashboard. Please try again.');
    }
  }

  // Real-time clock functionality
  function startRealTimeClock() {
    function updateClock() {
      const now = new Date();

      // Format time (12-hour format with AM/PM)
      const timeOptions = {
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: true
      };
      const timeString = now.toLocaleTimeString('en-US', timeOptions);

      // Format date
      const dateOptions = {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      };
      const dateString = now.toLocaleDateString('en-US', dateOptions);

      // Update display
      const timeElement = document.getElementById('current-time');
      const dateElement = document.getElementById('current-date');

      if (timeElement) timeElement.textContent = timeString;
      if (dateElement) dateElement.textContent = dateString;
    }

    // Update immediately and then every second
    updateClock();
    setInterval(updateClock, 1000);
  }
  
  function setupWindowControls() {
    try {
      const { ipcRenderer } = require('electron');
      
      const minimizeBtn = document.getElementById('minimize-btn');
      const maximizeBtn = document.getElementById('maximize-btn');
      const closeBtn = document.getElementById('close-btn');
      
      if (minimizeBtn) {
        minimizeBtn.addEventListener('click', () => {
          ipcRenderer.invoke('window-minimize');
        });
      }
      
      if (maximizeBtn) {
        maximizeBtn.addEventListener('click', () => {
          ipcRenderer.invoke('window-maximize');
        });
      }
      
      if (closeBtn) {
        closeBtn.addEventListener('click', () => {
          window.close();
        });
      }
    } catch (error) {
      console.log('Window controls not available (running in browser)');
    }
  }
  
  function setupThemeToggle() {
    const themeToggle = document.getElementById('theme-toggle');
    const themeIcon = themeToggle?.querySelector('.theme-icon');
    
    if (themeToggle) {
      themeToggle.addEventListener('click', () => {
        const currentTheme = document.documentElement.getAttribute('data-theme');
        const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
        
        document.documentElement.setAttribute('data-theme', newTheme);
        localStorage.setItem('theme', newTheme);
        
        if (themeIcon) {
          themeIcon.textContent = newTheme === 'dark' ? '☀️' : '🌙';
        }
      });
      
      // Load saved theme
      const savedTheme = localStorage.getItem('theme') || 'light';
      document.documentElement.setAttribute('data-theme', savedTheme);
      if (themeIcon) {
        themeIcon.textContent = savedTheme === 'dark' ? '☀️' : '🌙';
      }
    }
  }
  
  function updateTime() {
    const now = new Date();
    const timeElement = document.getElementById('current-time');
    const dateElement = document.getElementById('current-date');
    
    if (timeElement) {
      timeElement.textContent = now.toLocaleTimeString([], { 
        hour: '2-digit', 
        minute: '2-digit',
        hour12: true 
      });
    }
    
    if (dateElement) {
      dateElement.textContent = now.toLocaleDateString([], { 
        weekday: 'long',
        month: 'short',
        day: 'numeric'
      });
    }
  }
  
  function showSection(sectionName) {
    sections.forEach(section => {
      section.classList.remove('active');
    });
    
    const targetSection = document.getElementById(`${sectionName}-section`);
    if (targetSection) {
      targetSection.classList.add('active');
      
      // Load section-specific data
      loadSectionData(sectionName);
    }
  }
  
  function loadSectionData(section) {
    switch (section) {
      case 'tables':
        loadTables();
        break;
      case 'orders':
        loadOrders();
        break;
      case 'menu':
        loadMenuItems();
        break;
      case 'add-table':
        // Form is already loaded
        break;
      case 'add-menu':
        loadCategoriesForForm();
        break;
    }
  }
  
  async function loadTables() {
    try {
      const response = await axios.get('http://localhost:5000/api/tables');
      const tables = response.data;
      
      const tablesGrid = document.getElementById('tables-grid');
      if (tablesGrid) {
        tablesGrid.innerHTML = tables.map(table => `
          <div class="table-card ${table.status}" data-table-id="${table.id}">
            <div class="table-header">
              <div class="table-number">${table.table_number || table.id}</div>
              <div class="table-status ${table.status}">${table.status}</div>
            </div>
            <div class="table-info">
              <div class="table-name">${table.name}</div>
              <div class="table-details">
                <div class="table-capacity">
                  <span>👥</span>
                  <span>${table.capacity || 4} seats</span>
                </div>
                <div class="table-location">
                  <span>📍</span>
                  <span>${table.location || 'Indoor'}</span>
                </div>
              </div>
            </div>
            <div class="table-actions">
              ${table.status === 'available' ? 
                `<button class="table-action-btn primary" onclick="openTableOrder(${table.id}, '${table.name}')">Take Order</button>` :
                `<button class="table-action-btn secondary" onclick="viewTableOrder(${table.id})">View Order</button>`
              }
            </div>
          </div>
        `).join('');
      }
      
      // Setup table filters
      setupTableFilters(tables);
      
    } catch (error) {
      console.error('Error loading tables:', error);
      // Create some default tables if none exist
      createDefaultTables();
    }
  }
  
  function setupTableFilters(tables) {
    const filterBtns = document.querySelectorAll('.filter-btn');
    
    filterBtns.forEach(btn => {
      btn.addEventListener('click', () => {
        const filter = btn.getAttribute('data-filter');
        
        // Update active filter button
        filterBtns.forEach(b => b.classList.remove('active'));
        btn.classList.add('active');
        
        // Filter tables
        const tableCards = document.querySelectorAll('.table-card');
        tableCards.forEach(card => {
          if (filter === 'all' || card.classList.contains(filter)) {
            card.style.display = 'block';
          } else {
            card.style.display = 'none';
          }
        });
      });
    });
  }
  
  async function createDefaultTables() {
    const defaultTables = [
      { table_number: 1, name: 'Table 1', capacity: 4, location: 'indoor', status: 'available' },
      { table_number: 2, name: 'Table 2', capacity: 2, location: 'indoor', status: 'available' },
      { table_number: 3, name: 'Window Table', capacity: 6, location: 'indoor', status: 'available' },
      { table_number: 4, name: 'Patio Table', capacity: 4, location: 'outdoor', status: 'available' }
    ];
    
    try {
      for (const table of defaultTables) {
        await axios.post('http://localhost:5000/api/tables', table);
      }
      loadTables();
    } catch (error) {
      console.error('Error creating default tables:', error);
    }
  }
  
  async function loadCategories() {
    try {
      const response = await axios.get('http://localhost:5000/api/categories');
      return response.data || [];
    } catch (error) {
      console.error('Error loading categories:', error);
      return [];
    }
  }
  
  async function loadMenuItems() {
    try {
      const response = await axios.get('http://localhost:5000/api/products');
      const menuItems = response.data;
      
      const menuItemsContainer = document.getElementById('menu-items');
      if (menuItemsContainer) {
        menuItemsContainer.innerHTML = menuItems.map(item => `
          <div class="menu-item-card">
            <div class="menu-item-info">
              <h5>${item.name}</h5>
              <p>${item.category}</p>
            </div>
            <div class="menu-item-price">$${item.price.toFixed(2)}</div>
          </div>
        `).join('');
      }
    } catch (error) {
      console.error('Error loading menu items:', error);
    }
  }
  
  async function loadOrders() {
    // Placeholder for orders functionality
    const ordersContainer = document.getElementById('orders-container');
    if (ordersContainer) {
      ordersContainer.innerHTML = '<p>No active orders</p>';
    }
  }
  
  function setupFormHandlers() {
    // Add table form
    const addTableForm = document.getElementById('add-table-form');
    if (addTableForm) {
      addTableForm.addEventListener('submit', async (e) => {
        e.preventDefault();
        
        const tableData = {
          table_number: parseInt(document.getElementById('new-table-number').value),
          name: document.getElementById('new-table-name').value,
          capacity: parseInt(document.getElementById('new-table-capacity').value),
          location: document.getElementById('new-table-location').value,
          status: 'available'
        };
        
        try {
          await axios.post('http://localhost:5000/api/tables', tableData);
          addTableForm.reset();
          loadTables();
          alert('Table added successfully!');
        } catch (error) {
          console.error('Error adding table:', error);
          alert('Failed to add table: ' + (error.response?.data?.error || error.message));
        }
      });
    }
    
    // Add menu item form
    const addMenuForm = document.getElementById('add-menu-form');
    if (addMenuForm) {
      addMenuForm.addEventListener('submit', async (e) => {
        e.preventDefault();
        
        const menuData = {
          name: document.getElementById('new-menu-name').value,
          description: document.getElementById('new-menu-description').value,
          category: document.getElementById('new-menu-category').value,
          price: parseFloat(document.getElementById('new-menu-price').value),
          available: document.getElementById('new-menu-available').checked
        };
        
        try {
          await axios.post('http://localhost:5000/api/products', menuData);
          addMenuForm.reset();
          loadMenuItems();
          alert('Menu item added successfully!');
        } catch (error) {
          console.error('Error adding menu item:', error);
          alert('Failed to add menu item: ' + (error.response?.data?.error || error.message));
        }
      });
    }
  }
  
  async function loadCategoriesForForm() {
    const categories = await loadCategories();
    const categorySelect = document.getElementById('new-menu-category');
    
    if (categorySelect) {
      categorySelect.innerHTML = '<option value="">Select category</option>' +
        categories.map(cat => `<option value="${cat.name}">${cat.name}</option>`).join('');
    }
  }
  
  function logout() {
    localStorage.removeItem('authToken');
    localStorage.removeItem('userRole');
    localStorage.removeItem('username');
    window.location.href = 'index.html';
  }
  
  // Global functions for table operations
  window.openTableOrder = function(tableId, tableName) {
    currentOrder.tableId = tableId;
    currentOrder.tableName = tableName;
    
    const modal = document.getElementById('table-order-modal');
    const title = document.getElementById('table-order-title');
    
    if (title) {
      title.textContent = `${tableName} - Order`;
    }
    
    // Load menu items for ordering
    loadMenuForOrdering();
    
    // Show modal
    if (modal) {
      modal.style.display = 'block';
    }
  };
  
  window.closeTableOrderModal = function() {
    const modal = document.getElementById('table-order-modal');
    if (modal) {
      modal.style.display = 'none';
    }
  };
  
  window.clearTableForm = function() {
    document.getElementById('add-table-form').reset();
  };
  
  window.clearMenuForm = function() {
    document.getElementById('add-menu-form').reset();
  };
  
  async function loadMenuForOrdering() {
    try {
      // Load categories for filtering
      const categoriesResponse = await axios.get('http://localhost:5000/api/categories');
      const categories = categoriesResponse.data || [];

      const modalCategoriesContainer = document.getElementById('modal-menu-categories');
      if (modalCategoriesContainer) {
        modalCategoriesContainer.innerHTML = `
          <button class="category-filter-btn active" data-category="all">All Items</button>
          ${categories.map(cat => `
            <button class="category-filter-btn" data-category="${cat.name}">${cat.name}</button>
          `).join('')}
        `;

        // Add category filter event listeners
        modalCategoriesContainer.querySelectorAll('.category-filter-btn').forEach(btn => {
          btn.addEventListener('click', () => {
            // Update active category
            modalCategoriesContainer.querySelectorAll('.category-filter-btn').forEach(b => b.classList.remove('active'));
            btn.classList.add('active');

            // Filter menu items
            filterMenuItems(btn.getAttribute('data-category'));
          });
        });
      }

      // Load menu items
      const menuResponse = await axios.get('http://localhost:5000/api/products');
      const menuItems = menuResponse.data || [];

      const modalMenuItemsContainer = document.getElementById('modal-menu-items');
      if (modalMenuItemsContainer) {
        modalMenuItemsContainer.innerHTML = menuItems.map(item => `
          <div class="menu-item-card" data-category="${item.category}" onclick="addToOrder(${item.id}, '${item.name}', ${item.price})">
            <div class="menu-item-info">
              <h5>${item.name}</h5>
              <p>${item.description || item.category}</p>
            </div>
            <div class="menu-item-price">$${item.price.toFixed(2)}</div>
          </div>
        `).join('');
      }

      // Initialize order summary
      updateOrderSummary();

    } catch (error) {
      console.error('Error loading menu for ordering:', error);
    }
  }

  function filterMenuItems(category) {
    const menuItems = document.querySelectorAll('.menu-item-card');
    menuItems.forEach(item => {
      if (category === 'all' || item.getAttribute('data-category') === category) {
        item.style.display = 'flex';
      } else {
        item.style.display = 'none';
      }
    });
  }

  // Global functions for order management
  window.addToOrder = function(productId, productName, productPrice) {
    // Check if item already exists in order
    const existingItem = currentOrder.items.find(item => item.productId === productId);

    if (existingItem) {
      existingItem.quantity += 1;
      existingItem.totalPrice = existingItem.quantity * existingItem.unitPrice;
    } else {
      currentOrder.items.push({
        productId: productId,
        name: productName,
        unitPrice: productPrice,
        quantity: 1,
        totalPrice: productPrice
      });
    }

    updateOrderSummary();
  };

  window.removeFromOrder = function(productId) {
    currentOrder.items = currentOrder.items.filter(item => item.productId !== productId);
    updateOrderSummary();
  };

  window.updateQuantity = function(productId, change) {
    const item = currentOrder.items.find(item => item.productId === productId);
    if (item) {
      item.quantity += change;
      if (item.quantity <= 0) {
        removeFromOrder(productId);
      } else {
        item.totalPrice = item.quantity * item.unitPrice;
        updateOrderSummary();
      }
    }
  };

  function updateOrderSummary() {
    const orderItemsContainer = document.getElementById('order-items');
    const subtotalElement = document.getElementById('order-subtotal');
    const taxElement = document.getElementById('order-tax');
    const totalElement = document.getElementById('order-total');

    if (!orderItemsContainer) return;

    // Update items display
    orderItemsContainer.innerHTML = currentOrder.items.map(item => `
      <div class="order-item">
        <div class="order-item-info">
          <div class="order-item-name">${item.name}</div>
          <div class="order-item-price">$${item.unitPrice.toFixed(2)} each</div>
        </div>
        <div class="order-item-controls">
          <button class="quantity-btn" onclick="updateQuantity(${item.productId}, -1)">−</button>
          <span class="quantity-display">${item.quantity}</span>
          <button class="quantity-btn" onclick="updateQuantity(${item.productId}, 1)">+</button>
          <button class="quantity-btn" onclick="removeFromOrder(${item.productId})" style="background: #ef4444; margin-left: 8px;">×</button>
        </div>
      </div>
    `).join('');

    // Calculate totals
    currentOrder.subtotal = currentOrder.items.reduce((sum, item) => sum + item.totalPrice, 0);
    currentOrder.tax = currentOrder.subtotal * 0.0825; // 8.25% tax
    currentOrder.total = currentOrder.subtotal + currentOrder.tax;

    // Update totals display
    if (subtotalElement) subtotalElement.textContent = `$${currentOrder.subtotal.toFixed(2)}`;
    if (taxElement) taxElement.textContent = `$${currentOrder.tax.toFixed(2)}`;
    if (totalElement) totalElement.textContent = `$${currentOrder.total.toFixed(2)}`;
  }

  window.processOrder = async function() {
    if (currentOrder.items.length === 0) {
      alert('Please add items to the order first.');
      return;
    }

    try {
      const orderData = {
        table_id: currentOrder.tableId,
        items: currentOrder.items.map(item => ({
          product_id: item.productId,
          quantity: item.quantity,
          unit_price: item.unitPrice,
          total_price: item.totalPrice
        })),
        subtotal: currentOrder.subtotal,
        tax_amount: currentOrder.tax,
        total: currentOrder.total,
        status: 'completed'
      };

      const response = await axios.post('http://localhost:5000/api/orders', orderData);

      if (response.status === 201) {
        alert('Order processed successfully!');

        // Update table status to occupied
        await axios.put(`http://localhost:5000/api/tables/${currentOrder.tableId}`, {
          status: 'occupied'
        });

        // Clear order and close modal
        clearOrder();
        closeTableOrderModal();

        // Reload tables to show updated status
        loadTables();
      }
    } catch (error) {
      console.error('Error processing order:', error);
      alert('Failed to process order: ' + (error.response?.data?.error || error.message));
    }
  };

  window.saveOrder = async function() {
    if (currentOrder.items.length === 0) {
      alert('Please add items to the order first.');
      return;
    }

    try {
      const orderData = {
        table_id: currentOrder.tableId,
        items: currentOrder.items.map(item => ({
          product_id: item.productId,
          quantity: item.quantity,
          unit_price: item.unitPrice,
          total_price: item.totalPrice
        })),
        subtotal: currentOrder.subtotal,
        tax_amount: currentOrder.tax,
        total: currentOrder.total,
        status: 'pending'
      };

      const response = await axios.post('http://localhost:5000/api/orders', orderData);

      if (response.status === 201) {
        alert('Order saved successfully!');

        // Update table status to occupied
        await axios.put(`http://localhost:5000/api/tables/${currentOrder.tableId}`, {
          status: 'occupied'
        });

        // Clear order and close modal
        clearOrder();
        closeTableOrderModal();

        // Reload tables to show updated status
        loadTables();
      }
    } catch (error) {
      console.error('Error saving order:', error);
      alert('Failed to save order: ' + (error.response?.data?.error || error.message));
    }
  };

  window.clearOrder = function() {
    currentOrder.items = [];
    currentOrder.subtotal = 0;
    currentOrder.tax = 0;
    currentOrder.total = 0;
    updateOrderSummary();
  };

  window.viewTableOrder = function(tableId) {
    // This would load existing orders for the table
    alert('View existing orders functionality will be implemented next.');
  };
  
  function parseJwt(token) {
    try {
      return JSON.parse(atob(token.split('.')[1]));
    } catch (e) {
      return null;
    }
  }
});
