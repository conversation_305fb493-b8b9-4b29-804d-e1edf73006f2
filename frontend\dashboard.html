<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Cash Register Pro - Dashboard</title>
  <link rel="stylesheet" href="styles.css">
  <link rel="stylesheet" href="dashboard.css">
  <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body class="dashboard-body">
  <!-- Top Navigation Bar -->
  <nav class="top-nav">
    <div class="nav-left">
      <button class="sidebar-toggle" id="sidebar-toggle">
        <i class="fas fa-bars"></i>
      </button>
      <div class="logo-section">
        <span class="logo-icon">💰</span>
        <span class="logo-text">Cash Register Pro</span>
      </div>
    </div>
    
    <div class="nav-center">
      <div class="current-time" id="current-time"></div>
      <div class="current-date" id="current-date"></div>
    </div>
    
    <div class="nav-right">
      <div class="user-info">
        <span class="user-name" id="user-name">Admin User</span>
        <span class="user-role" id="user-role">Administrator</span>
      </div>
      <div class="user-avatar" id="user-avatar">
        <i class="fas fa-user-circle"></i>
      </div>
      <button class="logout-btn" id="logout-btn">
        <i class="fas fa-sign-out-alt"></i>
      </button>
    </div>
  </nav>

  <!-- Sidebar Navigation -->
  <aside class="sidebar" id="sidebar">
    <div class="sidebar-content">
      <!-- Main Navigation -->
      <nav class="sidebar-nav">
        <ul class="nav-list">
          <li class="nav-item active" data-page="dashboard">
            <a href="#" class="nav-link">
              <i class="fas fa-tachometer-alt"></i>
              <span class="nav-text">Dashboard</span>
            </a>
          </li>
          
          <li class="nav-item" data-page="take-order">
            <a href="#" class="nav-link">
              <i class="fas fa-shopping-cart"></i>
              <span class="nav-text">Take Order</span>
            </a>
          </li>
          
          <li class="nav-item" data-page="menu">
            <a href="#" class="nav-link">
              <i class="fas fa-utensils"></i>
              <span class="nav-text">Menu Management</span>
            </a>
          </li>
          
          <li class="nav-item" data-page="tables">
            <a href="#" class="nav-link">
              <i class="fas fa-table"></i>
              <span class="nav-text">Table Management</span>
            </a>
          </li>
          
          <li class="nav-item" data-page="orders">
            <a href="#" class="nav-link">
              <i class="fas fa-receipt"></i>
              <span class="nav-text">Orders</span>
            </a>
          </li>
          
          <li class="nav-item" data-page="reports">
            <a href="#" class="nav-link">
              <i class="fas fa-chart-bar"></i>
              <span class="nav-text">Reports</span>
            </a>
          </li>
          
          <li class="nav-item" data-page="inventory">
            <a href="#" class="nav-link">
              <i class="fas fa-boxes"></i>
              <span class="nav-text">Inventory</span>
            </a>
          </li>
          
          <li class="nav-item" data-page="customers">
            <a href="#" class="nav-link">
              <i class="fas fa-users"></i>
              <span class="nav-text">Customers</span>
            </a>
          </li>
          
          <li class="nav-item" data-page="settings">
            <a href="#" class="nav-link">
              <i class="fas fa-cog"></i>
              <span class="nav-text">Settings</span>
            </a>
          </li>
        </ul>
      </nav>
      
      <!-- Quick Actions -->
      <div class="quick-actions">
        <h4>Quick Actions</h4>
        <button class="quick-btn" id="quick-new-order">
          <i class="fas fa-plus"></i>
          <span>New Order</span>
        </button>
        <button class="quick-btn" id="quick-print-receipt">
          <i class="fas fa-print"></i>
          <span>Print Receipt</span>
        </button>
        <button class="quick-btn" id="quick-cash-drawer">
          <i class="fas fa-cash-register"></i>
          <span>Open Drawer</span>
        </button>
      </div>
    </div>
  </aside>

  <!-- Main Content Area -->
  <main class="main-content" id="main-content">
    <!-- Dashboard Page -->
    <div class="page active" id="dashboard-page">
      <div class="page-header">
        <h1>Dashboard</h1>
        <p>Welcome to Cash Register Pro - Your complete POS solution</p>
      </div>
      
      <div class="dashboard-grid">
        <!-- Stats Cards -->
        <div class="stats-row">
          <div class="stat-card">
            <div class="stat-icon">
              <i class="fas fa-dollar-sign"></i>
            </div>
            <div class="stat-content">
              <h3 id="today-sales">$0.00</h3>
              <p>Today's Sales</p>
            </div>
          </div>
          
          <div class="stat-card">
            <div class="stat-icon">
              <i class="fas fa-shopping-cart"></i>
            </div>
            <div class="stat-content">
              <h3 id="today-orders">0</h3>
              <p>Today's Orders</p>
            </div>
          </div>
          
          <div class="stat-card">
            <div class="stat-icon">
              <i class="fas fa-users"></i>
            </div>
            <div class="stat-content">
              <h3 id="today-customers">0</h3>
              <p>Today's Customers</p>
            </div>
          </div>
          
          <div class="stat-card">
            <div class="stat-icon">
              <i class="fas fa-table"></i>
            </div>
            <div class="stat-content">
              <h3 id="active-tables">0</h3>
              <p>Active Tables</p>
            </div>
          </div>
        </div>
        
        <!-- Recent Orders -->
        <div class="dashboard-section">
          <h2>Recent Orders</h2>
          <div class="recent-orders" id="recent-orders">
            <!-- Orders will be loaded here -->
          </div>
        </div>
        
        <!-- Quick Actions Grid -->
        <div class="dashboard-section">
          <h2>Quick Actions</h2>
          <div class="quick-actions-grid">
            <button class="action-card" data-action="new-order">
              <i class="fas fa-plus-circle"></i>
              <span>New Order</span>
            </button>
            <button class="action-card" data-action="view-menu">
              <i class="fas fa-utensils"></i>
              <span>View Menu</span>
            </button>
            <button class="action-card" data-action="manage-tables">
              <i class="fas fa-table"></i>
              <span>Manage Tables</span>
            </button>
            <button class="action-card" data-action="daily-report">
              <i class="fas fa-chart-line"></i>
              <span>Daily Report</span>
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Take Order Page -->
    <div class="page" id="take-order-page">
      <div class="page-header">
        <h1>Take Order</h1>
        <div class="order-controls">
          <button class="btn btn-secondary" id="clear-order">
            <i class="fas fa-trash"></i> Clear Order
          </button>
          <button class="btn btn-primary" id="process-order">
            <i class="fas fa-check"></i> Process Order
          </button>
        </div>
      </div>
      
      <div class="order-layout">
        <!-- Table Selection -->
        <div class="table-selection">
          <h3>Select Table</h3>
          <div class="table-grid" id="table-grid">
            <!-- Tables will be loaded here -->
          </div>
        </div>
        
        <!-- Menu Categories -->
        <div class="menu-section">
          <h3>Menu</h3>
          <div class="category-tabs" id="category-tabs">
            <!-- Categories will be loaded here -->
          </div>
          <div class="menu-items" id="menu-items">
            <!-- Menu items will be loaded here -->
          </div>
        </div>
        
        <!-- Order Summary -->
        <div class="order-summary">
          <h3>Order Summary</h3>
          <div class="selected-table" id="selected-table">
            <span>No table selected</span>
          </div>
          <div class="order-items" id="order-items">
            <!-- Order items will be added here -->
          </div>
          <div class="order-total">
            <div class="subtotal">
              <span>Subtotal:</span>
              <span id="order-subtotal">$0.00</span>
            </div>
            <div class="tax">
              <span>Tax (8%):</span>
              <span id="order-tax">$0.00</span>
            </div>
            <div class="total">
              <span>Total:</span>
              <span id="order-total">$0.00</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Menu Management Page -->
    <div class="page" id="menu-page">
      <div class="page-header">
        <h1>Menu Management</h1>
        <div class="menu-controls">
          <button class="btn btn-secondary" id="add-category-btn">
            <i class="fas fa-plus"></i> Add Category
          </button>
          <button class="btn btn-primary" id="add-item-btn">
            <i class="fas fa-plus"></i> Add Menu Item
          </button>
        </div>
      </div>

      <div class="menu-management">
        <div class="menu-categories">
          <h3>Categories</h3>
          <div class="categories-grid" id="categories-grid">
            <!-- Categories will be loaded here -->
          </div>
        </div>

        <div class="menu-items-management">
          <h3>Menu Items</h3>
          <div class="menu-filter">
            <select id="category-filter">
              <option value="all">All Categories</option>
            </select>
            <input type="text" id="item-search" placeholder="Search items...">
          </div>
          <div class="menu-items-grid" id="menu-items-grid">
            <!-- Menu items will be loaded here -->
          </div>
        </div>
      </div>
    </div>

    <!-- Table Management Page -->
    <div class="page" id="tables-page">
      <div class="page-header">
        <h1>Table Management</h1>
        <div class="table-controls">
          <button class="btn btn-secondary" id="clear-all-tables-btn">
            <i class="fas fa-broom"></i> Clear All Tables
          </button>
          <button class="btn btn-primary" id="add-table-btn">
            <i class="fas fa-plus"></i> Add Table
          </button>
        </div>
      </div>

      <div class="tables-management">
        <div class="table-stats">
          <div class="stat-item">
            <span class="stat-label">Total Tables:</span>
            <span class="stat-value" id="total-tables">0</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">Available:</span>
            <span class="stat-value available" id="available-tables">0</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">Occupied:</span>
            <span class="stat-value occupied" id="occupied-tables">0</span>
          </div>
        </div>

        <div class="tables-grid" id="tables-management-grid">
          <!-- Tables will be loaded here -->
        </div>
      </div>
    </div>

    <!-- Other pages will be added here -->
  </main>

  <!-- Modals and Overlays -->
  <div class="modal-overlay" id="modal-overlay"></div>

  <!-- Add Category Modal -->
  <div class="modal" id="add-category-modal">
    <div class="modal-content">
      <div class="modal-header">
        <h3>Add New Category</h3>
        <button class="modal-close" data-modal="add-category-modal">
          <i class="fas fa-times"></i>
        </button>
      </div>
      <div class="modal-body">
        <form id="add-category-form">
          <div class="form-group">
            <label for="category-name">Category Name</label>
            <input type="text" id="category-name" required>
          </div>
          <div class="form-group">
            <label for="category-icon">Icon (Emoji)</label>
            <input type="text" id="category-icon" placeholder="🍔" maxlength="2">
          </div>
          <div class="form-actions">
            <button type="button" class="btn btn-secondary" data-modal="add-category-modal">Cancel</button>
            <button type="submit" class="btn btn-primary">Add Category</button>
          </div>
        </form>
      </div>
    </div>
  </div>

  <!-- Add Menu Item Modal -->
  <div class="modal" id="add-item-modal">
    <div class="modal-content">
      <div class="modal-header">
        <h3>Add New Menu Item</h3>
        <button class="modal-close" data-modal="add-item-modal">
          <i class="fas fa-times"></i>
        </button>
      </div>
      <div class="modal-body">
        <form id="add-item-form">
          <div class="form-group">
            <label for="item-name">Item Name</label>
            <input type="text" id="item-name" required>
          </div>
          <div class="form-group">
            <label for="item-category">Category</label>
            <select id="item-category" required>
              <!-- Categories will be populated here -->
            </select>
          </div>
          <div class="form-group">
            <label for="item-price">Price ($)</label>
            <input type="number" id="item-price" step="0.01" min="0" required>
          </div>
          <div class="form-group">
            <label for="item-description">Description</label>
            <textarea id="item-description" rows="3"></textarea>
          </div>
          <div class="form-actions">
            <button type="button" class="btn btn-secondary" data-modal="add-item-modal">Cancel</button>
            <button type="submit" class="btn btn-primary">Add Item</button>
          </div>
        </form>
      </div>
    </div>
  </div>

  <!-- Add Table Modal -->
  <div class="modal" id="add-table-modal">
    <div class="modal-content">
      <div class="modal-header">
        <h3>Add New Table</h3>
        <button class="modal-close" data-modal="add-table-modal">
          <i class="fas fa-times"></i>
        </button>
      </div>
      <div class="modal-body">
        <form id="add-table-form">
          <div class="form-group">
            <label for="table-number">Table Number</label>
            <input type="number" id="table-number" min="1" required>
          </div>
          <div class="form-group">
            <label for="table-capacity">Capacity (Number of Seats)</label>
            <input type="number" id="table-capacity" min="1" max="20" value="4" required>
          </div>
          <div class="form-actions">
            <button type="button" class="btn btn-secondary" data-modal="add-table-modal">Cancel</button>
            <button type="submit" class="btn btn-primary">Add Table</button>
          </div>
        </form>
      </div>
    </div>
  </div>

  <!-- Notification Container -->
  <div class="notification-container" id="notification-container"></div>

  <!-- Scripts -->
  <script src="https://unpkg.com/axios/dist/axios.min.js"></script>
  <script src="enhanced-dashboard.js"></script>
</body>
</html>
