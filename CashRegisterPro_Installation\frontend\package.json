{"name": "cash-register-app-frontend", "version": "2.0.0", "description": "Professional Cash Register Desktop Application", "main": "main.js", "homepage": "https://github.com/yourusername/cash-register-app", "scripts": {"start": "electron .", "dev": "NODE_ENV=development electron .", "pack": "electron-builder --dir", "dist": "electron-builder", "dist:win": "electron-builder --win", "dist:mac": "electron-builder --mac", "dist:linux": "electron-builder --linux", "postinstall": "electron-builder install-app-deps", "clean": "<PERSON><PERSON><PERSON> dist build"}, "keywords": ["electron", "cash-register", "pos", "point-of-sale", "restaurant", "retail"], "author": {"name": "Your Name", "email": "<EMAIL>"}, "license": "MIT", "dependencies": {"electron-updater": "^6.1.7"}, "devDependencies": {"electron": "^28.0.0", "electron-builder": "^24.0.0", "electron-reload": "^1.5.0", "rimraf": "^3.0.2"}, "build": {"appId": "com.cashregister.app", "productName": "Cash Register Pro", "copyright": "Copyright © 2024 ${author}", "directories": {"output": "dist", "buildResources": "build"}, "files": ["**/*", "!dist/", "!node_modules/", "!build/", "!*.md", "!.git/", "!.giti<PERSON>re"], "extraResources": [{"from": "assets/", "to": "assets/", "filter": ["**/*"]}], "win": {"target": [{"target": "nsis", "arch": ["x64", "ia32"]}, {"target": "portable", "arch": ["x64"]}], "icon": "assets/icon.ico", "publisherName": "Cash Register Pro", "verifyUpdateCodeSignature": false}, "mac": {"target": [{"target": "dmg", "arch": ["x64", "arm64"]}], "icon": "assets/icon.icns", "category": "public.app-category.business"}, "linux": {"target": [{"target": "AppImage", "arch": ["x64"]}, {"target": "deb", "arch": ["x64"]}], "icon": "assets/icon.png", "category": "Office"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true, "shortcutName": "Cash Register Pro"}, "publish": {"provider": "github", "owner": "yourusername", "repo": "cash-register-app"}}}