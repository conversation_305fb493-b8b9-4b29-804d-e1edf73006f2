const axios = require('axios');
const { ipc<PERSON><PERSON><PERSON> } = require('electron');

// Global utilities
class NotificationManager {
  constructor() {
    this.container = document.getElementById('notification-container');
  }

  show(title, message, type = 'info', duration = 5000) {
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;

    notification.innerHTML = `
      <button class="notification-close">&times;</button>
      <div class="notification-title">${title}</div>
      <div class="notification-message">${message}</div>
    `;

    this.container.appendChild(notification);

    // Auto remove
    const timer = setTimeout(() => {
      this.remove(notification);
    }, duration);

    // Manual close
    notification.querySelector('.notification-close').addEventListener('click', () => {
      clearTimeout(timer);
      this.remove(notification);
    });

    return notification;
  }

  remove(notification) {
    notification.style.animation = 'slideOutRight 0.3s ease-in';
    setTimeout(() => {
      if (notification.parentNode) {
        notification.parentNode.removeChild(notification);
      }
    }, 300);
  }
}

// Theme management
class ThemeManager {
  constructor() {
    this.currentTheme = localStorage.getItem('theme') || 'light';
    this.applyTheme();
    this.setupToggle();
  }

  applyTheme() {
    document.documentElement.setAttribute('data-theme', this.currentTheme);
    const themeIcon = document.querySelector('.theme-icon');
    if (themeIcon) {
      themeIcon.textContent = this.currentTheme === 'light' ? '🌙' : '☀️';
    }
  }

  toggle() {
    this.currentTheme = this.currentTheme === 'light' ? 'dark' : 'light';
    localStorage.setItem('theme', this.currentTheme);
    this.applyTheme();
  }

  setupToggle() {
    const toggleBtn = document.getElementById('theme-toggle');
    if (toggleBtn) {
      toggleBtn.addEventListener('click', () => this.toggle());
    }
  }
}

// Loading manager
class LoadingManager {
  constructor() {
    this.overlay = document.getElementById('loading-overlay');
  }

  show(message = 'Loading...') {
    if (this.overlay) {
      this.overlay.querySelector('p').textContent = message;
      this.overlay.style.display = 'flex';
    }
  }

  hide() {
    if (this.overlay) {
      this.overlay.style.display = 'none';
    }
  }
}

// Initialize global managers
const notifications = new NotificationManager();
const themeManager = new ThemeManager();
const loadingManager = new LoadingManager();

// Window controls
function setupWindowControls() {
  const minimizeBtn = document.getElementById('minimize-btn');
  const maximizeBtn = document.getElementById('maximize-btn');
  const closeBtn = document.getElementById('close-btn');

  if (minimizeBtn) {
    minimizeBtn.addEventListener('click', () => {
      ipcRenderer.invoke('window-minimize');
    });
  }

  if (maximizeBtn) {
    maximizeBtn.addEventListener('click', () => {
      ipcRenderer.invoke('window-maximize');
    });
  }

  if (closeBtn) {
    closeBtn.addEventListener('click', () => {
      ipcRenderer.invoke('window-close');
    });
  }
}

// Enhanced form validation
function validateForm(formData) {
  const errors = [];

  if (!formData.username || formData.username.length < 3) {
    errors.push('Username must be at least 3 characters long');
  }

  if (!formData.password || formData.password.length < 6) {
    errors.push('Password must be at least 6 characters long');
  }

  return errors;
}

// Backend connectivity test
async function testBackendConnection() {
  try {
    console.log('Testing backend connection...');
    const response = await axios.get('http://localhost:5000/', { timeout: 5000 });
    console.log('Backend connection successful:', response.data);
    return true;
  } catch (error) {
    console.error('Backend connection failed:', error);
    return false;
  }
}

// Main login functionality
document.addEventListener('DOMContentLoaded', async () => {
  setupWindowControls();

  // Test backend connection on load
  const backendConnected = await testBackendConnection();
  if (!backendConnected) {
    notifications.show(
      'Backend Connection Error',
      'Cannot connect to the backend server. Please ensure the backend is running.',
      'error'
    );
  }

  const loginBtn = document.getElementById('login-btn');
  const usernameInput = document.getElementById('username');
  const passwordInput = document.getElementById('password');
  const errorMessage = document.getElementById('error-message');
  const btnText = loginBtn.querySelector('.btn-text');
  const btnSpinner = loginBtn.querySelector('.loading-spinner');

  console.log('Login elements found:', {
    loginBtn: !!loginBtn,
    usernameInput: !!usernameInput,
    passwordInput: !!passwordInput,
    errorMessage: !!errorMessage,
    btnText: !!btnText,
    btnSpinner: !!btnSpinner
  });

  // Auto-focus username field
  if (usernameInput) {
    usernameInput.focus();
  }

  // Form submission
  const handleLogin = (event) => {
    if (event) {
      event.preventDefault();
    }

    console.log('Login button clicked'); // Debug log

    const username = usernameInput.value.trim();
    const password = passwordInput.value.trim();

    console.log('Username:', username, 'Password length:', password.length); // Debug log

    // Clear previous errors
    errorMessage.textContent = '';

    // Validate form
    const errors = validateForm({ username, password });
    if (errors.length > 0) {
      console.log('Validation errors:', errors); // Debug log
      errorMessage.textContent = errors[0];
      notifications.show('Validation Error', errors[0], 'error');
      return;
    }

    console.log('Calling login function'); // Debug log
    login(username, password);
  };

  // Event listeners
  loginBtn.addEventListener('click', handleLogin);

  // Add ripple effect to login button
  loginBtn.addEventListener('mousedown', (e) => {
    const ripple = loginBtn.querySelector('.btn-ripple');
    if (ripple) {
      ripple.style.background = 'rgba(255, 255, 255, 0.3)';
      setTimeout(() => {
        ripple.style.background = '';
      }, 600);
    }
  });

  // Enter key support for both fields
  [usernameInput, passwordInput].forEach(input => {
    input.addEventListener('keypress', (event) => {
      if (event.key === 'Enter') {
        handleLogin();
      }
    });
  });

  // Real-time validation feedback
  [usernameInput, passwordInput].forEach(input => {
    input.addEventListener('input', () => {
      if (errorMessage.textContent) {
        errorMessage.textContent = '';
      }
    });
  });

  async function login(username, password) {
    try {
      console.log('Starting login process...'); // Debug log

      // Show loading state
      loginBtn.disabled = true;
      btnText.style.display = 'none';
      btnSpinner.style.display = 'inline-block';
      loadingManager.show('Authenticating...');

      console.log('Sending login request to backend...'); // Debug log

      const response = await axios.post('http://localhost:5000/api/auth/login', {
        username,
        password
      }, {
        timeout: 10000, // 10 second timeout
        headers: {
          'Content-Type': 'application/json'
        }
      });

      console.log('Login response received:', response.data); // Debug log

      if (response.data.token) {
        // Store auth data
        localStorage.setItem('authToken', response.data.token);
        localStorage.setItem('userRole', response.data.role);
        localStorage.setItem('username', response.data.username);

        // Show success notification
        notifications.show(
          'Login Successful',
          `Welcome back, ${response.data.username}!`,
          'success'
        );

        // Redirect to appropriate dashboard based on role
        setTimeout(() => {
          if (response.data.role === 'admin') {
            window.location.href = 'admin-dashboard.html';
          } else {
            window.location.href = 'main-dashboard.html';
          }
        }, 1000);
      }
    } catch (error) {
      console.error('Login error:', error);

      let errorMsg = 'Login failed. Please try again.';

      if (error.code === 'ECONNREFUSED') {
        errorMsg = 'Cannot connect to server. Please ensure the backend is running.';
      } else if (error.response?.data?.error) {
        errorMsg = error.response.data.error;
      } else if (error.code === 'ECONNABORTED') {
        errorMsg = 'Request timeout. Please check your connection.';
      }

      errorMessage.textContent = errorMsg;
      notifications.show('Login Failed', errorMsg, 'error');

      // Shake animation for form
      document.querySelector('.login-form').style.animation = 'shake 0.5s ease-in-out';
      setTimeout(() => {
        document.querySelector('.login-form').style.animation = '';
      }, 500);

    } finally {
      // Reset loading state
      loginBtn.disabled = false;
      btnText.style.display = 'inline';
      btnSpinner.style.display = 'none';
      loadingManager.hide();
    }
  }

  // Check if already logged in
  const token = localStorage.getItem('authToken');
  const role = localStorage.getItem('userRole');

  if (token && role) {
    // Verify token is still valid
    axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;

    axios.get('http://localhost:5000/api/auth/verify')
      .then(() => {
        // Token is valid, redirect to appropriate dashboard
        if (role === 'admin') {
          window.location.href = 'admin-dashboard.html';
        } else {
          window.location.href = 'main-dashboard.html';
        }
      })
      .catch(() => {
        // Token is invalid, clear storage
        localStorage.clear();
      });
  }
});

// Global error handler
window.addEventListener('error', (event) => {
  console.error('Global error:', event.error);
  notifications.show(
    'Application Error',
    'An unexpected error occurred. Please refresh the application.',
    'error'
  );
});

// Export for use in other modules
window.notifications = notifications;
window.themeManager = themeManager;
window.loadingManager = loadingManager;
