from flask import Blueprint, request, jsonify
from db.models import db, Category, Product
from functools import wraps
import jwt
import os

category_bp = Blueprint('category', __name__)

def token_required(f):
    @wraps(f)
    def decorated(*args, **kwargs):
        token = request.headers.get('Authorization')
        if not token:
            return jsonify({'error': 'Token is missing'}), 401
        
        try:
            if token.startswith('Bearer '):
                token = token[7:]
            data = jwt.decode(token, os.getenv('SECRET_KEY', 'your-secret-key'), algorithms=['HS256'])
        except:
            return jsonify({'error': 'Token is invalid'}), 401
        
        return f(*args, **kwargs)
    return decorated

def admin_required(f):
    @wraps(f)
    def decorated(*args, **kwargs):
        token = request.headers.get('Authorization')
        if not token:
            return jsonify({'error': 'Token is missing'}), 401
        
        try:
            if token.startswith('Bearer '):
                token = token[7:]
            data = jwt.decode(token, os.getenv('SECRET_KEY', 'your-secret-key'), algorithms=['HS256'])
            if data.get('role') != 'admin':
                return jsonify({'error': 'Admin access required'}), 403
        except:
            return jsonify({'error': 'Token is invalid'}), 401
        
        return f(*args, **kwargs)
    return decorated

@category_bp.route('/categories', methods=['GET'])
@token_required
def get_categories():
    """Get all categories with item counts"""
    try:
        categories = Category.query.all()
        result = []
        
        for category in categories:
            # Count products in this category
            product_count = Product.query.filter_by(category_id=category.id).count()
            
            result.append({
                'id': category.id,
                'name': category.name,
                'description': category.description,
                'items_count': product_count,
                'created_at': category.created_at.isoformat() if category.created_at else None
            })
        
        return jsonify(result), 200
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@category_bp.route('/categories', methods=['POST'])
@admin_required
def create_category():
    """Create a new category"""
    try:
        data = request.get_json()
        
        if not data or not data.get('name'):
            return jsonify({'error': 'Category name is required'}), 400
        
        # Check if category already exists
        existing_category = Category.query.filter_by(name=data['name']).first()
        if existing_category:
            return jsonify({'error': 'Category already exists'}), 400
        
        category = Category(
            name=data['name'],
            description=data.get('description', '')
        )
        
        db.session.add(category)
        db.session.commit()
        
        return jsonify({
            'id': category.id,
            'name': category.name,
            'description': category.description,
            'items_count': 0,
            'created_at': category.created_at.isoformat()
        }), 201
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@category_bp.route('/categories/<int:category_id>', methods=['GET'])
@token_required
def get_category(category_id):
    """Get a specific category"""
    try:
        category = Category.query.get_or_404(category_id)
        product_count = Product.query.filter_by(category_id=category.id).count()
        
        return jsonify({
            'id': category.id,
            'name': category.name,
            'description': category.description,
            'items_count': product_count,
            'created_at': category.created_at.isoformat() if category.created_at else None
        }), 200
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@category_bp.route('/categories/<int:category_id>', methods=['PUT'])
@admin_required
def update_category(category_id):
    """Update a category"""
    try:
        category = Category.query.get_or_404(category_id)
        data = request.get_json()
        
        if not data:
            return jsonify({'error': 'No data provided'}), 400
        
        # Check if new name conflicts with existing category
        if 'name' in data and data['name'] != category.name:
            existing_category = Category.query.filter_by(name=data['name']).first()
            if existing_category:
                return jsonify({'error': 'Category name already exists'}), 400
            category.name = data['name']
        
        if 'description' in data:
            category.description = data['description']
        
        db.session.commit()
        
        product_count = Product.query.filter_by(category_id=category.id).count()
        
        return jsonify({
            'id': category.id,
            'name': category.name,
            'description': category.description,
            'items_count': product_count,
            'created_at': category.created_at.isoformat() if category.created_at else None
        }), 200
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@category_bp.route('/categories/<int:category_id>', methods=['DELETE'])
@admin_required
def delete_category(category_id):
    """Delete a category"""
    try:
        category = Category.query.get_or_404(category_id)
        
        # Check if category has products
        product_count = Product.query.filter_by(category_id=category.id).count()
        if product_count > 0:
            return jsonify({'error': f'Cannot delete category with {product_count} products. Move or delete products first.'}), 400
        
        db.session.delete(category)
        db.session.commit()
        
        return jsonify({'message': 'Category deleted successfully'}), 200
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@category_bp.route('/categories/<int:category_id>/products', methods=['GET'])
@token_required
def get_category_products(category_id):
    """Get all products in a category"""
    try:
        category = Category.query.get_or_404(category_id)
        products = Product.query.filter_by(category_id=category.id).all()
        
        result = []
        for product in products:
            result.append({
                'id': product.id,
                'name': product.name,
                'description': product.description,
                'category': product.category,
                'price': product.price,
                'available': product.available,
                'created_at': product.created_at.isoformat() if product.created_at else None,
                'updated_at': product.updated_at.isoformat() if product.updated_at else None
            })
        
        return jsonify({
            'category': {
                'id': category.id,
                'name': category.name,
                'description': category.description
            },
            'products': result
        }), 200
    except Exception as e:
        return jsonify({'error': str(e)}), 500
