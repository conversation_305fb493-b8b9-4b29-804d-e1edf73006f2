
from flask import Flask, request, jsonify
from flask_cors import CORS
from flask_sqlalchemy import SQLAlchemy
from werkzeug.security import generate_password_hash, check_password_hash
import jwt
import os
from datetime import datetime, timedelta
from functools import wraps

# Initialize Flask app
app = Flask(__name__)
app.config['SECRET_KEY'] = 'cash_register_secret_key_2024'
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///cash_register.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

# Initialize extensions
db = SQLAlchemy(app)
CORS(app)

# Database Models
class User(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    password_hash = db.Column(db.String(120), nullable=False)
    role = db.Column(db.String(20), nullable=False, default='cashier')
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    def set_password(self, password):
        self.password_hash = generate_password_hash(password)
    
    def check_password(self, password):
        return check_password_hash(self.password_hash, password)

class Category(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    description = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

class Product(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    description = db.Column(db.Text)
    price = db.Column(db.Float, nullable=False)
    category_id = db.Column(db.Integer, db.ForeignKey('category.id'))
    available = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

class Table(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    number = db.Column(db.Integer, unique=True, nullable=False)
    name = db.Column(db.String(100), nullable=False)
    capacity = db.Column(db.Integer, nullable=False)
    location = db.Column(db.String(50))
    status = db.Column(db.String(20), default='available')
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

class Order(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    table_id = db.Column(db.Integer, db.ForeignKey('table.id'))
    subtotal = db.Column(db.Float, nullable=False)
    tax_amount = db.Column(db.Float, nullable=False)
    total = db.Column(db.Float, nullable=False)
    status = db.Column(db.String(20), default='pending')
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

class OrderItem(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    order_id = db.Column(db.Integer, db.ForeignKey('order.id'))
    product_id = db.Column(db.Integer, db.ForeignKey('product.id'))
    quantity = db.Column(db.Integer, nullable=False)
    unit_price = db.Column(db.Float, nullable=False)
    total_price = db.Column(db.Float, nullable=False)

# Authentication decorator
def token_required(f):
    @wraps(f)
    def decorated(*args, **kwargs):
        token = request.headers.get('Authorization')
        if not token:
            return jsonify({'error': 'Token is missing'}), 401
        try:
            if token.startswith('Bearer '):
                token = token[7:]
            data = jwt.decode(token, app.config['SECRET_KEY'], algorithms=['HS256'])
            current_user = User.query.get(data['user_id'])
        except:
            return jsonify({'error': 'Token is invalid'}), 401
        return f(current_user, *args, **kwargs)
    return decorated

# API Routes
@app.route('/')
def home():
    return {'message': 'Cash Register Pro API', 'status': 'running'}

@app.route('/api/auth/login', methods=['POST'])
def login():
    data = request.get_json()
    user = User.query.filter_by(username=data.get('username')).first()
    
    if user and user.check_password(data.get('password')):
        token = jwt.encode({
            'user_id': user.id,
            'role': user.role,
            'exp': datetime.utcnow() + timedelta(hours=24)
        }, app.config['SECRET_KEY'])
        
        return jsonify({
            'token': token,
            'user': {'id': user.id, 'username': user.username, 'role': user.role}
        })
    
    return jsonify({'error': 'Invalid credentials'}), 401

@app.route('/api/auth/verify', methods=['GET'])
def verify_token():
    token = request.headers.get('Authorization')
    if not token:
        return jsonify({'error': 'Token is missing'}), 401
    try:
        if token.startswith('Bearer '):
            token = token[7:]
        data = jwt.decode(token, app.config['SECRET_KEY'], algorithms=['HS256'])
        current_user = User.query.get(data['user_id'])
        if current_user:
            return jsonify({'user': {'id': current_user.id, 'username': current_user.username, 'role': current_user.role}})
        else:
            return jsonify({'error': 'User not found'}), 401
    except:
        return jsonify({'error': 'Token is invalid'}), 401

@app.route('/api/categories', methods=['GET', 'POST'])
def handle_categories():
    if request.method == 'GET':
        categories = Category.query.all()
        return jsonify([{'id': c.id, 'name': c.name, 'description': c.description} for c in categories])
    
    elif request.method == 'POST':
        data = request.get_json()
        category = Category(name=data['name'], description=data.get('description', ''))
        db.session.add(category)
        db.session.commit()
        return jsonify({'id': category.id, 'name': category.name}), 201

@app.route('/api/products', methods=['GET', 'POST'])
def handle_products():
    if request.method == 'GET':
        products = Product.query.all()
        return jsonify([{
            'id': p.id, 'name': p.name, 'description': p.description,
            'price': p.price, 'category_id': p.category_id, 'available': p.available
        } for p in products])
    
    elif request.method == 'POST':
        data = request.get_json()
        product = Product(
            name=data['name'],
            description=data.get('description', ''),
            price=float(data['price']),
            category_id=data.get('category_id'),
            available=data.get('available', True)
        )
        db.session.add(product)
        db.session.commit()
        return jsonify({'id': product.id, 'name': product.name}), 201

@app.route('/api/tables', methods=['GET', 'POST'])
def handle_tables():
    if request.method == 'GET':
        tables = Table.query.all()
        return jsonify([{
            'id': t.id, 'number': t.number, 'name': t.name,
            'capacity': t.capacity, 'location': t.location, 'status': t.status
        } for t in tables])
    
    elif request.method == 'POST':
        data = request.get_json()
        table = Table(
            number=data['number'],
            name=data['name'],
            capacity=data['capacity'],
            location=data.get('location', 'indoor')
        )
        db.session.add(table)
        db.session.commit()
        return jsonify({'id': table.id, 'number': table.number}), 201

@app.route('/api/tables/<int:table_id>', methods=['PUT'])
def update_table(table_id):
    table = Table.query.get_or_404(table_id)
    data = request.get_json()
    table.status = data.get('status', table.status)
    db.session.commit()
    return jsonify({'id': table.id, 'status': table.status})

@app.route('/api/orders', methods=['GET', 'POST'])
def handle_orders():
    if request.method == 'GET':
        orders = Order.query.all()
        return jsonify([{
            'id': o.id, 'table_id': o.table_id, 'subtotal': o.subtotal,
            'tax_amount': o.tax_amount, 'total': o.total, 'status': o.status,
            'created_at': o.created_at.isoformat()
        } for o in orders])
    
    elif request.method == 'POST':
        data = request.get_json()
        order = Order(
            table_id=data['table_id'],
            subtotal=data['subtotal'],
            tax_amount=data['tax_amount'],
            total=data['total'],
            status=data.get('status', 'pending')
        )
        db.session.add(order)
        db.session.commit()
        
        # Add order items
        for item in data.get('items', []):
            order_item = OrderItem(
                order_id=order.id,
                product_id=item['product_id'],
                quantity=item['quantity'],
                unit_price=item['unit_price'],
                total_price=item['total_price']
            )
            db.session.add(order_item)
        
        db.session.commit()
        return jsonify({'id': order.id, 'status': order.status}), 201

@app.route('/api/sales/daily', methods=['GET'])
def daily_sales():
    today = datetime.utcnow().date()
    orders = Order.query.filter(
        db.func.date(Order.created_at) == today,
        Order.status == 'completed'
    ).all()
    
    total_sales = sum(order.total for order in orders)
    return jsonify({
        'date': today.isoformat(),
        'total_sales': total_sales,
        'order_count': len(orders)
    })

def init_db():
    """Initialize database with sample data"""
    db.create_all()
    
    # Create admin user
    if not User.query.filter_by(username='admin').first():
        admin = User(username='admin', role='admin')
        admin.set_password('admin123')
        db.session.add(admin)
    
    # Create cashier user
    if not User.query.filter_by(username='cashier').first():
        cashier = User(username='cashier', role='cashier')
        cashier.set_password('cashier123')
        db.session.add(cashier)
    
    # Create sample categories
    if Category.query.count() == 0:
        categories = [
            Category(name='Appetizers', description='Starters and small plates'),
            Category(name='Main Courses', description='Main dishes and entrees'),
            Category(name='Beverages', description='Drinks and beverages'),
            Category(name='Desserts', description='Sweet treats and desserts')
        ]
        for cat in categories:
            db.session.add(cat)
    
    # Create sample products
    if Product.query.count() == 0:
        products = [
            Product(name='Caesar Salad', description='Fresh romaine lettuce with caesar dressing', price=12.99, category_id=1),
            Product(name='Grilled Chicken', description='Herb-seasoned grilled chicken breast', price=18.99, category_id=2),
            Product(name='Coca Cola', description='Classic soft drink', price=2.99, category_id=3),
            Product(name='Chocolate Cake', description='Rich chocolate layer cake', price=6.99, category_id=4)
        ]
        for prod in products:
            db.session.add(prod)
    
    # Create sample tables
    if Table.query.count() == 0:
        tables = [
            Table(number=1, name='Window Table 1', capacity=4, location='indoor'),
            Table(number=2, name='Corner Table', capacity=2, location='indoor'),
            Table(number=3, name='Patio Table 1', capacity=6, location='outdoor'),
            Table(number=4, name='Bar Table', capacity=2, location='bar')
        ]
        for table in tables:
            db.session.add(table)
    
    db.session.commit()
    print("Database initialized with sample data")

if __name__ == '__main__':
    print("Starting Cash Register Pro Backend...")
    init_db()
    print("Server running on http://localhost:5000")
    app.run(debug=False, host='0.0.0.0', port=5000, use_reloader=False)
