from flask import Blueprint, request, jsonify
from db.models import Table, db
from api.auth import token_required, admin_required, roles_required

table = Blueprint('table', __name__)

@table.route('/tables', methods=['GET'])
@roles_required(['admin', 'cashier'])
def get_tables(current_user):
    """Get all tables"""
    try:
        tables = Table.query.all()
        result = []
        
        for table in tables:
            table_data = {
                'id': table.id,
                'table_number': table.table_number,
                'name': table.name,
                'capacity': table.capacity,
                'location': table.location,
                'status': table.status,
                'created_at': table.created_at.isoformat() if table.created_at else None
            }
            result.append(table_data)
        
        return jsonify(result)
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@table.route('/tables/<int:table_id>', methods=['GET'])
@roles_required(['admin', 'cashier'])
def get_table(current_user, table_id):
    """Get a specific table"""
    try:
        table = Table.query.get(table_id)
        
        if not table:
            return jsonify({'error': 'Table not found!'}), 404
        
        table_data = {
            'id': table.id,
            'table_number': table.table_number,
            'name': table.name,
            'capacity': table.capacity,
            'location': table.location,
            'status': table.status,
            'created_at': table.created_at.isoformat() if table.created_at else None
        }
        
        return jsonify(table_data)
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@table.route('/tables', methods=['POST'])
@admin_required
def create_table(current_user):
    """Create a new table (admin only)"""
    try:
        data = request.get_json()
        
        if not data or not data.get('name') or not data.get('table_number'):
            return jsonify({'error': 'Table name and table number are required!'}), 400

        # Check if table number already exists
        existing_table = Table.query.filter_by(table_number=data['table_number']).first()
        if existing_table:
            return jsonify({'error': 'Table number already exists!'}), 400

        # Set default values if not provided
        status = data.get('status', 'available')
        capacity = data.get('capacity', 4)
        location = data.get('location', 'indoor')

        # Validate status
        if status not in ['available', 'occupied', 'reserved', 'maintenance']:
            return jsonify({'error': 'Invalid status!'}), 400

        # Validate location
        if location not in ['indoor', 'outdoor', 'patio', 'private', 'bar']:
            return jsonify({'error': 'Invalid location!'}), 400

        # Create new table
        new_table = Table(
            table_number=data['table_number'],
            name=data['name'],
            capacity=capacity,
            location=location,
            status=status
        )
        
        db.session.add(new_table)
        db.session.commit()
        
        return jsonify({
            'message': 'Table created successfully!',
            'table_id': new_table.id
        }), 201
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@table.route('/tables/<int:table_id>', methods=['PUT'])
@admin_required
def update_table(current_user, table_id):
    """Update a table (admin only)"""
    try:
        data = request.get_json()
        table = Table.query.get(table_id)
        
        if not table:
            return jsonify({'error': 'Table not found!'}), 404
        
        # Update table number if provided
        if data.get('table_number') and data['table_number'] != table.table_number:
            existing_table = Table.query.filter_by(table_number=data['table_number']).first()
            if existing_table:
                return jsonify({'error': 'Table number already exists!'}), 400
            table.table_number = data['table_number']

        # Update name if provided
        if data.get('name'):
            table.name = data['name']

        # Update capacity if provided
        if data.get('capacity'):
            table.capacity = data['capacity']

        # Update location if provided
        if data.get('location'):
            if data['location'] not in ['indoor', 'outdoor', 'patio', 'private', 'bar']:
                return jsonify({'error': 'Invalid location!'}), 400
            table.location = data['location']

        # Update status if provided
        if data.get('status'):
            if data['status'] not in ['available', 'occupied', 'reserved', 'maintenance']:
                return jsonify({'error': 'Invalid status!'}), 400
            table.status = data['status']
        
        db.session.commit()
        
        return jsonify({'message': 'Table updated successfully!'})
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@table.route('/tables/<int:table_id>', methods=['DELETE'])
@admin_required
def delete_table(current_user, table_id):
    """Delete a table (admin only)"""
    try:
        table = Table.query.get(table_id)
        
        if not table:
            return jsonify({'error': 'Table not found!'}), 404
        
        # Check if table has active orders
        if table.status == 'occupied':
            return jsonify({'error': 'Cannot delete a table with active orders!'}), 400
        
        db.session.delete(table)
        db.session.commit()
        
        return jsonify({'message': 'Table deleted successfully!'})
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500
