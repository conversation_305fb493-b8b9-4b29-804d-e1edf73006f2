/* Enhanced Dashboard Styles for Cash Register Pro */

/* Dashboard Container */
.dashboard-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: var(--bg-secondary);
  overflow: hidden;
}

/* Enhanced Header */
.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-lg) var(--spacing-xl);
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
  color: white;
  box-shadow: var(--shadow-lg);
  z-index: 100;
  position: relative;
}

.header-left {
  display: flex;
  align-items: center;
  gap: var(--spacing-lg);
}

.logo-section {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.logo-icon {
  font-size: 2.5rem;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
}

.header-title h1 {
  margin: 0;
  font-size: 1.8rem;
  font-weight: 800;
  letter-spacing: -0.02em;
}

.header-subtitle {
  font-size: 0.9rem;
  opacity: 0.9;
  font-weight: 500;
}

.header-right {
  display: flex;
  align-items: center;
  gap: var(--spacing-lg);
}

.user-info {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  padding: var(--spacing-sm) var(--spacing-md);
  background: rgba(255, 255, 255, 0.1);
  border-radius: var(--radius-lg);
  backdrop-filter: blur(10px);
}

.user-avatar {
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
}

.user-details {
  display: flex;
  flex-direction: column;
}

.user-name {
  font-weight: 600;
  font-size: 0.9rem;
}

.user-role {
  font-size: 0.8rem;
  opacity: 0.8;
}

.logout-btn {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm) var(--spacing-md);
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: var(--radius-md);
  color: white;
  font-weight: 500;
  transition: all var(--transition-fast);
  backdrop-filter: blur(10px);
}

.logout-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-1px);
}

/* Admin Content Layout */
.admin-content {
  display: flex;
  flex: 1;
  overflow: hidden;
}

/* Enhanced Sidebar */
.admin-sidebar {
  width: 280px;
  background: var(--bg-primary);
  border-right: 1px solid var(--border-light);
  display: flex;
  flex-direction: column;
  transition: width var(--transition-normal);
  position: relative;
  z-index: 50;
  box-shadow: var(--shadow-md);
}

.admin-sidebar.collapsed {
  width: 70px;
}

.sidebar-toggle {
  padding: var(--spacing-md);
  text-align: center;
  border-bottom: 1px solid var(--border-light);
  cursor: pointer;
  transition: all var(--transition-fast);
}

.sidebar-toggle:hover {
  background: var(--bg-secondary);
}

.toggle-icon {
  font-size: 1.2rem;
  color: var(--text-secondary);
}

.sidebar-nav {
  flex: 1;
  padding: var(--spacing-md) 0;
  overflow-y: auto;
}

.nav-section {
  margin-bottom: var(--spacing-xl);
}

.nav-section-title {
  padding: 0 var(--spacing-lg);
  margin-bottom: var(--spacing-md);
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  color: var(--text-muted);
  transition: opacity var(--transition-fast);
}

.admin-sidebar.collapsed .nav-section-title {
  opacity: 0;
}

.nav-btn {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  width: 100%;
  padding: var(--spacing-md) var(--spacing-lg);
  background: none;
  border: none;
  color: var(--text-secondary);
  font-weight: 500;
  text-align: left;
  transition: all var(--transition-fast);
  position: relative;
  cursor: pointer;
}

.nav-btn::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 3px;
  background: var(--primary-color);
  transform: scaleY(0);
  transition: transform var(--transition-fast);
}

.nav-btn:hover,
.nav-btn.active {
  background: var(--primary-light);
  color: var(--primary-color);
}

.nav-btn.active::before {
  transform: scaleY(1);
}

.nav-icon {
  font-size: 1.1rem;
  min-width: 20px;
  text-align: center;
}

.nav-text {
  transition: opacity var(--transition-fast);
}

.admin-sidebar.collapsed .nav-text {
  opacity: 0;
}

/* Main Content Area */
.admin-main {
  flex: 1;
  padding: var(--spacing-xl);
  overflow-y: auto;
  background: var(--bg-secondary);
}

/* Section Styles */
.admin-section {
  display: none;
  animation: fadeIn 0.3s ease-in-out;
}

.admin-section.active {
  display: block;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

.section-header {
  margin-bottom: var(--spacing-2xl);
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  flex-wrap: wrap;
  gap: var(--spacing-md);
}

.section-header h2 {
  margin: 0;
  font-size: 2rem;
  font-weight: 700;
  color: var(--text-primary);
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.section-description {
  color: var(--text-secondary);
  margin: var(--spacing-sm) 0 0 0;
  font-size: 1rem;
}

/* Dashboard Stats */
.dashboard-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-2xl);
}

.stat-card {
  background: var(--bg-primary);
  padding: var(--spacing-xl);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--border-light);
  display: flex;
  align-items: center;
  gap: var(--spacing-lg);
  transition: all var(--transition-fast);
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.stat-icon {
  font-size: 2.5rem;
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, var(--primary-light) 0%, var(--secondary-light) 100%);
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
}

.stat-content h3 {
  margin: 0;
  font-size: 2rem;
  font-weight: 700;
  color: var(--text-primary);
}

.stat-content p {
  margin: var(--spacing-xs) 0 0 0;
  color: var(--text-secondary);
  font-weight: 500;
}

/* Chart Card */
.dashboard-charts {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--spacing-lg);
}

.chart-card {
  background: var(--bg-primary);
  padding: var(--spacing-xl);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--border-light);
}

.chart-card h3 {
  margin: 0 0 var(--spacing-lg) 0;
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--text-primary);
}

.activity-list {
  max-height: 300px;
  overflow-y: auto;
}

/* Enhanced Buttons */
.add-btn {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-md) var(--spacing-lg);
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%);
  color: white;
  border: none;
  border-radius: var(--radius-md);
  font-weight: 600;
  cursor: pointer;
  transition: all var(--transition-fast);
  box-shadow: var(--shadow-sm);
}

.add-btn:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.btn-icon {
  font-size: 1.1rem;
}

/* Enhanced Tables */
.table-container {
  background: var(--bg-primary);
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--border-light);
}

.data-table {
  width: 100%;
  border-collapse: collapse;
}

.data-table th {
  background: var(--bg-tertiary);
  padding: var(--spacing-lg);
  text-align: left;
  font-weight: 600;
  color: var(--text-primary);
  border-bottom: 1px solid var(--border-light);
}

.data-table td {
  padding: var(--spacing-lg);
  border-bottom: 1px solid var(--border-light);
  color: var(--text-secondary);
}

.data-table tr:hover {
  background: var(--bg-secondary);
}

/* Enhanced Modals */
.modal {
  display: none;
  position: fixed;
  z-index: 1000;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(5px);
}

.modal-content {
  background: var(--bg-primary);
  margin: 5% auto;
  padding: 0;
  border-radius: var(--radius-lg);
  width: 90%;
  max-width: 500px;
  box-shadow: var(--shadow-2xl);
  animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
  from { opacity: 0; transform: translateY(-30px); }
  to { opacity: 1; transform: translateY(0); }
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-xl);
  border-bottom: 1px solid var(--border-light);
}

.modal-header h3 {
  margin: 0;
  font-size: 1.3rem;
  font-weight: 600;
  color: var(--text-primary);
}

.close-btn {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: var(--text-muted);
  transition: color var(--transition-fast);
}

.close-btn:hover {
  color: var(--text-primary);
}

.modal-form {
  padding: var(--spacing-xl);
}

.form-actions {
  display: flex;
  gap: var(--spacing-md);
  justify-content: flex-end;
  margin-top: var(--spacing-xl);
}

.btn-primary {
  padding: var(--spacing-md) var(--spacing-lg);
  background: var(--primary-color);
  color: white;
  border: none;
  border-radius: var(--radius-md);
  font-weight: 600;
  cursor: pointer;
  transition: all var(--transition-fast);
}

.btn-primary:hover {
  background: var(--primary-hover);
  transform: translateY(-1px);
}

.btn-secondary {
  padding: var(--spacing-md) var(--spacing-lg);
  background: var(--bg-secondary);
  color: var(--text-secondary);
  border: 1px solid var(--border-medium);
  border-radius: var(--radius-md);
  font-weight: 600;
  cursor: pointer;
  transition: all var(--transition-fast);
}

.btn-secondary:hover {
  background: var(--bg-tertiary);
  color: var(--text-primary);
}

/* Field Hints */
.field-hint {
  display: block;
  margin-top: var(--spacing-xs);
  font-size: 0.8rem;
  color: var(--text-muted);
}

/* Settings Grid */
.settings-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: var(--spacing-xl);
}

.setting-card {
  background: var(--bg-primary);
  padding: var(--spacing-xl);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--border-light);
}

.setting-card h3 {
  margin: 0 0 var(--spacing-lg) 0;
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--text-primary);
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

/* Responsive Design */
@media (max-width: 1024px) {
  .admin-sidebar {
    width: 70px;
  }

  .admin-sidebar .nav-text,
  .admin-sidebar .nav-section-title {
    display: none;
  }

  .dashboard-stats {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  }
}

@media (max-width: 768px) {
  .admin-content {
    flex-direction: column;
  }

  .admin-sidebar {
    width: 100%;
    height: auto;
    flex-direction: row;
    overflow-x: auto;
  }

  .sidebar-nav {
    display: flex;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
  }

  .nav-section {
    margin: 0;
  }

  .nav-btn {
    white-space: nowrap;
    min-width: 120px;
  }

  .admin-main {
    padding: var(--spacing-md);
  }

  .section-header {
    flex-direction: column;
    align-items: flex-start;
  }

  .dashboard-stats {
    grid-template-columns: 1fr;
  }

  .settings-grid {
    grid-template-columns: 1fr;
  }
}

.logout-btn {
  background: var(--danger-color);
  color: white;
  border: none;
  padding: 10px 15px;
  border-radius: 8px;
  cursor: pointer;
  transition: all var(--transition-normal);
  font-size: 1rem;
}

.logout-btn:hover {
  background: var(--danger-hover);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

/* Sidebar */
.sidebar {
  position: fixed;
  top: 70px;
  left: 0;
  width: 280px;
  height: calc(100vh - 70px);
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-right: 1px solid rgba(0, 0, 0, 0.1);
  transform: translateX(0);
  transition: transform var(--transition-normal);
  z-index: 999;
  overflow-y: auto;
  box-shadow: 2px 0 20px rgba(0, 0, 0, 0.1);
}

.sidebar.collapsed {
  transform: translateX(-280px);
}

.sidebar-content {
  padding: 20px;
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 30px;
}

/* Navigation */
.nav-list {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.nav-item {
  border-radius: 12px;
  overflow: hidden;
  transition: all var(--transition-normal);
}

.nav-link {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 15px 20px;
  text-decoration: none;
  color: var(--text-primary);
  transition: all var(--transition-normal);
  border-radius: 12px;
  position: relative;
  overflow: hidden;
}

.nav-link::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(99, 102, 241, 0.1), transparent);
  transition: left var(--transition-normal);
}

.nav-link:hover::before {
  left: 100%;
}

.nav-link i {
  font-size: 1.2rem;
  width: 20px;
  text-align: center;
  color: var(--primary-color);
}

.nav-text {
  font-weight: 500;
  font-size: 1rem;
}

.nav-item:hover {
  transform: translateX(5px);
}

.nav-item.active .nav-link {
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  color: white;
  box-shadow: var(--shadow-md);
}

.nav-item.active .nav-link i {
  color: white;
}

/* Quick Actions */
.quick-actions {
  margin-top: auto;
  padding-top: 20px;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
}

.quick-actions h4 {
  margin: 0 0 15px 0;
  color: var(--text-secondary);
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.quick-btn {
  display: flex;
  align-items: center;
  gap: 10px;
  width: 100%;
  padding: 12px 15px;
  background: var(--bg-secondary);
  border: none;
  border-radius: 8px;
  cursor: pointer;
  transition: all var(--transition-normal);
  margin-bottom: 8px;
  color: var(--text-primary);
  font-weight: 500;
}

.quick-btn:hover {
  background: var(--primary-light);
  transform: translateY(-2px);
  box-shadow: var(--shadow-sm);
}

.quick-btn i {
  color: var(--primary-color);
}

/* Main Content */
.main-content {
  margin-left: 280px;
  margin-top: 70px;
  padding: 30px;
  min-height: calc(100vh - 70px);
  transition: margin-left var(--transition-normal);
}

.main-content.expanded {
  margin-left: 0;
}

/* Pages */
.page {
  display: none;
  animation: fadeIn 0.3s ease-in-out;
}

.page.active {
  display: block;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

/* Page Header */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 2px solid var(--border-light);
}

.page-header h1 {
  margin: 0;
  font-size: 2.5rem;
  font-weight: 700;
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.page-header p {
  margin: 5px 0 0 0;
  color: var(--text-secondary);
  font-size: 1.1rem;
}

/* Buttons */
.btn {
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 600;
  font-size: 1rem;
  transition: all var(--transition-normal);
  display: inline-flex;
  align-items: center;
  gap: 8px;
  text-decoration: none;
  position: relative;
  overflow: hidden;
}

.btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  transition: left var(--transition-normal);
}

.btn:hover::before {
  left: 100%;
}

.btn-primary {
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  color: white;
  box-shadow: var(--shadow-md);
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.btn-secondary {
  background: var(--bg-secondary);
  color: var(--text-primary);
  border: 2px solid var(--border-medium);
}

.btn-secondary:hover {
  background: var(--bg-tertiary);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

/* Dashboard Grid */
.dashboard-grid {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

/* Stats Row */
.stats-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.stat-card {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20px);
  padding: 25px;
  border-radius: 16px;
  box-shadow: var(--shadow-md);
  display: flex;
  align-items: center;
  gap: 20px;
  transition: all var(--transition-normal);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.stat-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-lg);
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.5rem;
}

.stat-content h3 {
  margin: 0;
  font-size: 2rem;
  font-weight: 700;
  color: var(--text-primary);
}

.stat-content p {
  margin: 5px 0 0 0;
  color: var(--text-secondary);
  font-weight: 500;
}

/* Dashboard Sections */
.dashboard-section {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20px);
  padding: 30px;
  border-radius: 16px;
  box-shadow: var(--shadow-md);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.dashboard-section h2 {
  margin: 0 0 20px 0;
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-primary);
}

/* Quick Actions Grid */
.quick-actions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
}

.action-card {
  background: var(--bg-secondary);
  border: 2px solid var(--border-light);
  border-radius: 12px;
  padding: 30px 20px;
  cursor: pointer;
  transition: all var(--transition-normal);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 15px;
  text-align: center;
  color: var(--text-primary);
  font-weight: 600;
}

.action-card:hover {
  background: var(--primary-light);
  border-color: var(--primary-color);
  transform: translateY(-5px);
  box-shadow: var(--shadow-md);
}

.action-card i {
  font-size: 2.5rem;
  color: var(--primary-color);
}

/* Order Layout */
.order-layout {
  display: grid;
  grid-template-columns: 1fr 2fr 1fr;
  gap: 30px;
  height: calc(100vh - 200px);
}

.table-selection, .menu-section, .order-summary {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20px);
  padding: 25px;
  border-radius: 16px;
  box-shadow: var(--shadow-md);
  border: 1px solid rgba(255, 255, 255, 0.2);
  overflow-y: auto;
}

.table-selection h3, .menu-section h3, .order-summary h3 {
  margin: 0 0 20px 0;
  font-size: 1.3rem;
  font-weight: 600;
  color: var(--text-primary);
  border-bottom: 2px solid var(--primary-color);
  padding-bottom: 10px;
}

/* Table Grid */
.table-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
  gap: 15px;
}

.table-item {
  aspect-ratio: 1;
  background: var(--bg-secondary);
  border: 2px solid var(--border-light);
  border-radius: 12px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all var(--transition-normal);
  font-weight: 600;
  position: relative;
}

.table-item:hover {
  background: var(--primary-light);
  border-color: var(--primary-color);
  transform: scale(1.05);
}

.table-item.selected {
  background: var(--primary-color);
  color: white;
  border-color: var(--primary-dark);
}

.table-item.occupied {
  background: var(--danger-light);
  border-color: var(--danger-color);
}

.table-number {
  font-size: 1.2rem;
  font-weight: 700;
}

.table-status {
  font-size: 0.8rem;
  margin-top: 5px;
}

/* Menu Categories */
.category-tabs {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
  flex-wrap: wrap;
}

.category-tab {
  padding: 10px 20px;
  background: var(--bg-secondary);
  border: 2px solid var(--border-light);
  border-radius: 25px;
  cursor: pointer;
  transition: all var(--transition-normal);
  font-weight: 500;
  white-space: nowrap;
}

.category-tab:hover {
  background: var(--primary-light);
  border-color: var(--primary-color);
}

.category-tab.active {
  background: var(--primary-color);
  color: white;
  border-color: var(--primary-dark);
}

/* Menu Items */
.menu-items {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 15px;
}

.menu-item {
  background: var(--bg-secondary);
  border: 2px solid var(--border-light);
  border-radius: 12px;
  padding: 15px;
  cursor: pointer;
  transition: all var(--transition-normal);
  text-align: center;
}

.menu-item:hover {
  background: var(--primary-light);
  border-color: var(--primary-color);
  transform: translateY(-3px);
  box-shadow: var(--shadow-md);
}

.menu-item-name {
  font-weight: 600;
  margin-bottom: 8px;
  color: var(--text-primary);
}

.menu-item-price {
  font-size: 1.2rem;
  font-weight: 700;
  color: var(--primary-color);
}

.menu-item-description {
  font-size: 0.9rem;
  color: var(--text-secondary);
  margin-top: 5px;
}

/* Order Summary */
.selected-table {
  background: var(--primary-light);
  padding: 15px;
  border-radius: 8px;
  margin-bottom: 20px;
  text-align: center;
  font-weight: 600;
  color: var(--primary-color);
}

.order-items {
  max-height: 300px;
  overflow-y: auto;
  margin-bottom: 20px;
}

.order-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  background: var(--bg-secondary);
  border-radius: 8px;
  margin-bottom: 10px;
}

.order-item-info {
  flex: 1;
}

.order-item-name {
  font-weight: 600;
  color: var(--text-primary);
}

.order-item-price {
  color: var(--text-secondary);
  font-size: 0.9rem;
}

.order-item-controls {
  display: flex;
  align-items: center;
  gap: 10px;
}

.quantity-btn {
  width: 30px;
  height: 30px;
  border: none;
  border-radius: 50%;
  background: var(--primary-color);
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all var(--transition-normal);
}

.quantity-btn:hover {
  background: var(--primary-hover);
  transform: scale(1.1);
}

.quantity-display {
  font-weight: 600;
  min-width: 30px;
  text-align: center;
}

.order-total {
  border-top: 2px solid var(--border-light);
  padding-top: 15px;
}

.order-total > div {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.total {
  font-weight: 700;
  font-size: 1.2rem;
  color: var(--primary-color);
  border-top: 1px solid var(--border-light);
  padding-top: 8px;
  margin-top: 8px;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .sidebar {
    transform: translateX(-280px);
  }

  .sidebar.open {
    transform: translateX(0);
  }

  .main-content {
    margin-left: 0;
  }

  .order-layout {
    grid-template-columns: 1fr;
    height: auto;
  }
}

@media (max-width: 768px) {
  .top-nav {
    padding: 0 15px;
  }

  .nav-center {
    display: none;
  }

  .main-content {
    padding: 20px 15px;
  }

  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }

  .stats-row {
    grid-template-columns: 1fr;
  }

  .quick-actions-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .menu-items {
    grid-template-columns: 1fr;
  }

  .table-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

/* Notification System */
.notification-container {
  position: fixed;
  top: 90px;
  right: 20px;
  z-index: 10000;
  display: flex;
  flex-direction: column;
  gap: 10px;
  max-width: 400px;
}

.notification {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 12px;
  padding: 15px 20px;
  box-shadow: var(--shadow-lg);
  border-left: 4px solid;
  display: flex;
  align-items: center;
  gap: 12px;
  animation: slideInRight 0.3s ease-out;
  position: relative;
  overflow: hidden;
}

.notification::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
  transform: translateX(-100%);
  animation: shimmer 2s infinite;
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes shimmer {
  to {
    transform: translateX(100%);
  }
}

.notification-success {
  border-left-color: var(--success-color);
}

.notification-error {
  border-left-color: var(--danger-color);
}

.notification-warning {
  border-left-color: var(--warning-color);
}

.notification-info {
  border-left-color: var(--info-color);
}

.notification i {
  font-size: 1.2rem;
}

.notification-success i {
  color: var(--success-color);
}

.notification-error i {
  color: var(--danger-color);
}

.notification-warning i {
  color: var(--warning-color);
}

.notification-info i {
  color: var(--info-color);
}

.notification span {
  flex: 1;
  font-weight: 500;
  color: var(--text-primary);
}

.notification-close {
  background: none;
  border: none;
  font-size: 1.2rem;
  color: var(--text-secondary);
  cursor: pointer;
  padding: 0;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all var(--transition-normal);
}

.notification-close:hover {
  background: var(--bg-tertiary);
  color: var(--text-primary);
}

/* Recent Orders Styling */
.recent-order-item {
  background: var(--bg-secondary);
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 10px;
  border-left: 3px solid var(--primary-color);
  transition: all var(--transition-normal);
}

.recent-order-item:hover {
  background: var(--bg-tertiary);
  transform: translateX(5px);
}

.order-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.order-id {
  font-weight: 700;
  color: var(--primary-color);
}

.order-table {
  font-weight: 600;
  color: var(--text-primary);
}

.order-total {
  font-weight: 700;
  color: var(--success-color);
  font-size: 1.1rem;
}

.order-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.order-time {
  color: var(--text-secondary);
  font-size: 0.9rem;
}

.order-status {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
}

.status-completed {
  background: var(--success-light);
  color: var(--success-color);
}

.status-preparing {
  background: var(--warning-light);
  color: var(--warning-color);
}

.status-served {
  background: var(--info-light);
  color: var(--info-color);
}

/* Modal System */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(5px);
  z-index: 9999;
  display: none;
  opacity: 0;
  transition: opacity var(--transition-normal);
}

.modal-overlay.active {
  display: flex;
  opacity: 1;
}

.modal {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) scale(0.9);
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 16px;
  box-shadow: var(--shadow-2xl);
  z-index: 10000;
  display: none;
  opacity: 0;
  transition: all var(--transition-normal);
  max-width: 500px;
  width: 90%;
  max-height: 80vh;
  overflow-y: auto;
}

.modal.active {
  display: block;
  opacity: 1;
  transform: translate(-50%, -50%) scale(1);
}

.modal-content {
  padding: 0;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 25px;
  border-bottom: 1px solid var(--border-light);
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  color: white;
  border-radius: 16px 16px 0 0;
}

.modal-header h3 {
  margin: 0;
  font-size: 1.3rem;
  font-weight: 600;
}

.modal-close {
  background: none;
  border: none;
  color: white;
  font-size: 1.2rem;
  cursor: pointer;
  padding: 5px;
  border-radius: 50%;
  transition: all var(--transition-normal);
}

.modal-close:hover {
  background: rgba(255, 255, 255, 0.2);
}

.modal-body {
  padding: 25px;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 600;
  color: var(--text-primary);
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 12px 15px;
  border: 2px solid var(--border-light);
  border-radius: 8px;
  font-size: 1rem;
  transition: all var(--transition-normal);
  background: var(--bg-primary);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.form-actions {
  display: flex;
  gap: 15px;
  justify-content: flex-end;
  margin-top: 25px;
  padding-top: 20px;
  border-top: 1px solid var(--border-light);
}

/* Menu Management Styles */
.menu-management {
  display: grid;
  grid-template-columns: 1fr 2fr;
  gap: 30px;
  height: calc(100vh - 250px);
}

.menu-categories,
.menu-items-management {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20px);
  padding: 25px;
  border-radius: 16px;
  box-shadow: var(--shadow-md);
  border: 1px solid rgba(255, 255, 255, 0.2);
  overflow-y: auto;
}

.menu-categories h3,
.menu-items-management h3 {
  margin: 0 0 20px 0;
  font-size: 1.3rem;
  font-weight: 600;
  color: var(--text-primary);
  border-bottom: 2px solid var(--primary-color);
  padding-bottom: 10px;
}

.categories-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  gap: 15px;
}

.category-card {
  background: var(--bg-secondary);
  border: 2px solid var(--border-light);
  border-radius: 12px;
  padding: 20px;
  text-align: center;
  cursor: pointer;
  transition: all var(--transition-normal);
  position: relative;
}

.category-card:hover {
  background: var(--primary-light);
  border-color: var(--primary-color);
  transform: translateY(-3px);
  box-shadow: var(--shadow-md);
}

.category-icon {
  font-size: 2rem;
  margin-bottom: 10px;
  display: block;
}

.category-name {
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 5px;
}

.category-count {
  font-size: 0.9rem;
  color: var(--text-secondary);
}

.category-actions {
  position: absolute;
  top: 10px;
  right: 10px;
  display: flex;
  gap: 5px;
  opacity: 0;
  transition: opacity var(--transition-normal);
}

.category-card:hover .category-actions {
  opacity: 1;
}

.action-btn {
  width: 25px;
  height: 25px;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.8rem;
  transition: all var(--transition-normal);
}

.edit-btn {
  background: var(--info-color);
  color: white;
}

.delete-btn {
  background: var(--danger-color);
  color: white;
}

.action-btn:hover {
  transform: scale(1.1);
}

.menu-filter {
  display: flex;
  gap: 15px;
  margin-bottom: 20px;
}

.menu-filter select,
.menu-filter input {
  flex: 1;
  padding: 10px 15px;
  border: 2px solid var(--border-light);
  border-radius: 8px;
  font-size: 1rem;
}

.menu-items-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 15px;
}

.menu-item-card {
  background: var(--bg-secondary);
  border: 2px solid var(--border-light);
  border-radius: 12px;
  padding: 20px;
  transition: all var(--transition-normal);
  position: relative;
}

.menu-item-card:hover {
  background: var(--bg-tertiary);
  border-color: var(--primary-color);
  transform: translateY(-3px);
  box-shadow: var(--shadow-md);
}

.menu-item-card .category-actions {
  top: 15px;
  right: 15px;
}

.menu-item-card:hover .category-actions {
  opacity: 1;
}

.item-name {
  font-weight: 600;
  font-size: 1.1rem;
  color: var(--text-primary);
  margin-bottom: 8px;
}

.item-price {
  font-size: 1.3rem;
  font-weight: 700;
  color: var(--primary-color);
  margin-bottom: 8px;
}

.item-description {
  font-size: 0.9rem;
  color: var(--text-secondary);
  line-height: 1.4;
}

.item-category-badge {
  position: absolute;
  top: 15px;
  left: 15px;
  background: var(--primary-color);
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 600;
}

/* Table Management Styles */
.table-stats {
  display: flex;
  gap: 30px;
  margin-bottom: 30px;
  padding: 20px;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20px);
  border-radius: 12px;
  box-shadow: var(--shadow-md);
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 5px;
}

.stat-label {
  font-size: 0.9rem;
  color: var(--text-secondary);
  font-weight: 500;
}

.stat-value {
  font-size: 2rem;
  font-weight: 700;
  color: var(--text-primary);
}

.stat-value.available {
  color: var(--success-color);
}

.stat-value.occupied {
  color: var(--danger-color);
}

.tables-management .tables-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: 20px;
  padding: 25px;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20px);
  border-radius: 16px;
  box-shadow: var(--shadow-md);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.table-management-item {
  aspect-ratio: 1;
  background: var(--bg-secondary);
  border: 2px solid var(--border-light);
  border-radius: 12px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all var(--transition-normal);
  position: relative;
  padding: 15px;
}

.table-management-item:hover {
  background: var(--primary-light);
  border-color: var(--primary-color);
  transform: scale(1.05);
}

.table-management-item.occupied {
  background: var(--danger-light);
  border-color: var(--danger-color);
}

.table-management-item.available {
  background: var(--success-light);
  border-color: var(--success-color);
}

.table-management-number {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 5px;
}

.table-management-capacity {
  font-size: 0.9rem;
  color: var(--text-secondary);
  margin-bottom: 5px;
}

.table-management-status {
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
  padding: 3px 8px;
  border-radius: 10px;
}

.table-management-item.available .table-management-status {
  background: var(--success-color);
  color: white;
}

.table-management-item.occupied .table-management-status {
  background: var(--danger-color);
  color: white;
}

.table-management-actions {
  position: absolute;
  top: 8px;
  right: 8px;
  display: flex;
  gap: 3px;
  opacity: 0;
  transition: opacity var(--transition-normal);
}

.table-management-item:hover .table-management-actions {
  opacity: 1;
}

/* Enhanced Accessibility */
.nav-item:focus,
.btn:focus,
.menu-item:focus,
.table-item:focus,
.category-card:focus,
.menu-item-card:focus,
.table-management-item:focus {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .login-form,
  .sidebar,
  .top-nav,
  .stat-card,
  .dashboard-section {
    border: 2px solid var(--text-primary);
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}
