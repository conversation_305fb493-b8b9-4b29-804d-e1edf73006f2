#!/usr/bin/env python3
"""
Simple backend starter for Cash Register Pro
"""

import sys
import os
import time

# Add current directory to path
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

# Change to backend directory
os.chdir(current_dir)

def start_backend():
    try:
        print("🚀 Starting Cash Register Pro Backend...")
        
        from flask import Flask
        from flask_cors import CORS
        from api.auth import auth
        from api.product import product
        from api.category import category_bp
        from api.order import order
        from api.table import table
        from api.sales import sales
        from api.inventory import inventory
        from api.customers import customers
        from db.models import db, init_db
        from config import Config
        
        app = Flask(__name__)
        app.config.from_object(Config)
        
        # Initialize extensions
        db.init_app(app)
        CORS(app)
        
        # Register Blueprints
        app.register_blueprint(auth, url_prefix='/api/auth')
        app.register_blueprint(product, url_prefix='/api')
        app.register_blueprint(category_bp, url_prefix='/api')
        app.register_blueprint(order, url_prefix='/api')
        app.register_blueprint(table, url_prefix='/api')
        app.register_blueprint(sales, url_prefix='/api')
        app.register_blueprint(inventory, url_prefix='/api')
        app.register_blueprint(customers, url_prefix='/api')
        
        @app.route('/')
        def home():
            return {"message": "Cash Register API Running", "status": "success"}
        
        @app.route('/health')
        def health():
            return {"status": "healthy", "message": "Backend is running"}
        
        print("📦 Initializing database...")
        with app.app_context():
            init_db()
        print("✅ Database initialized successfully!")
        
        port = int(os.environ.get('PORT', 5000))
        print(f"🌐 Starting server on http://localhost:{port}")
        print("✅ Backend is ready!")
        
        app.run(debug=False, host='0.0.0.0', port=port, use_reloader=False)
        
    except Exception as e:
        print(f"❌ Error starting backend: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True

if __name__ == '__main__':
    success = start_backend()
    if not success:
        input("Press Enter to exit...")
        sys.exit(1)
