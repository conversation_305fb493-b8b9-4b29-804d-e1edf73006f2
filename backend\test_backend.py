#!/usr/bin/env python3
"""
Test script to debug backend issues
"""

import sys
import os

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    print("Testing imports...")
    
    print("1. Testing Flask import...")
    from flask import Flask
    print("✓ Flask imported")
    
    print("2. Testing CORS import...")
    from flask_cors import CORS
    print("✓ CORS imported")
    
    print("3. Testing config import...")
    from config import Config
    print("✓ Config imported")
    
    print("4. Testing models import...")
    from db.models import db, init_db
    print("✓ Models imported")
    
    print("5. Testing API imports...")
    from api.auth import auth
    print("✓ Auth API imported")
    
    from api.product import product
    print("✓ Product API imported")
    
    from api.category import category_bp
    print("✓ Category API imported")
    
    from api.order import order
    print("✓ Order API imported")
    
    from api.table import table
    print("✓ Table API imported")
    
    from api.sales import sales
    print("✓ Sales API imported")
    
    from api.inventory import inventory
    print("✓ Inventory API imported")
    
    from api.customers import customers
    print("✓ Customers API imported")
    
    print("\n6. Testing app creation...")
    app = Flask(__name__)
    app.config.from_object(Config)
    print("✓ App created and configured")
    
    print("7. Testing database initialization...")
    db.init_app(app)
    CORS(app)
    print("✓ Extensions initialized")
    
    print("8. Testing database setup...")
    with app.app_context():
        init_db()
    print("✓ Database initialized")
    
    print("\n✅ All tests passed! Backend should work correctly.")
    
except Exception as e:
    print(f"\n❌ Error: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)
