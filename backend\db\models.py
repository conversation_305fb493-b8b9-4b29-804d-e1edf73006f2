from flask_sqlalchemy import SQLAlchemy
from werkzeug.security import generate_password_hash, check_password_hash
from datetime import datetime

db = SQLAlchemy()

class User(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(50), unique=True, nullable=False)
    password_hash = db.Column(db.String(128), nullable=False)
    role = db.Column(db.String(20), nullable=False)  # 'admin' or 'cashier'
    
    def __init__(self, username, password, role):
        self.username = username
        self.password_hash = generate_password_hash(password)
        self.role = role
    
    def check_password(self, password):
        return check_password_hash(self.password_hash, password)

class Category(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(50), unique=True, nullable=False)
    description = db.Column(db.Text)
    created_at = db.Column(db.DateTime, nullable=False, default=datetime.now)

    products = db.relationship('Product', backref='category_ref', lazy=True)

class Product(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    description = db.Column(db.Text)
    category = db.Column(db.String(50), nullable=False)
    category_id = db.Column(db.Integer, db.ForeignKey('category.id'), nullable=True)
    price = db.Column(db.Float, nullable=False)
    available = db.Column(db.Boolean, nullable=False, default=True)
    created_at = db.Column(db.DateTime, nullable=False, default=datetime.now)
    updated_at = db.Column(db.DateTime, nullable=False, default=datetime.now, onupdate=datetime.now)

    order_items = db.relationship('OrderItem', backref='product', lazy=True)

class Table(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    table_number = db.Column(db.Integer, unique=True, nullable=False)
    name = db.Column(db.String(50), nullable=False)
    capacity = db.Column(db.Integer, nullable=False, default=4)
    location = db.Column(db.String(50), nullable=False, default='indoor')  # 'indoor', 'outdoor', 'patio', 'private', 'bar'
    status = db.Column(db.String(20), nullable=False, default='available')  # 'available', 'occupied', 'reserved', 'maintenance'
    created_at = db.Column(db.DateTime, nullable=False, default=datetime.now)

    orders = db.relationship('Order', backref='table', lazy=True)

class Order(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    table_id = db.Column(db.Integer, db.ForeignKey('table.id'), nullable=False)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=True)  # Cashier who took the order
    date = db.Column(db.DateTime, nullable=False, default=datetime.now)
    subtotal = db.Column(db.Float, nullable=False, default=0.0)
    tax_rate = db.Column(db.Float, nullable=False, default=0.0825)  # 8.25% default tax
    tax_amount = db.Column(db.Float, nullable=False, default=0.0)
    total = db.Column(db.Float, nullable=False, default=0.0)
    status = db.Column(db.String(20), nullable=False, default='pending')  # 'pending', 'completed', 'cancelled'
    payment_method = db.Column(db.String(20), nullable=True)  # 'cash', 'card', 'digital'
    notes = db.Column(db.Text)
    created_at = db.Column(db.DateTime, nullable=False, default=datetime.now)
    updated_at = db.Column(db.DateTime, nullable=False, default=datetime.now, onupdate=datetime.now)

    items = db.relationship('OrderItem', backref='order', lazy=True, cascade="all, delete-orphan")
    user = db.relationship('User', backref='orders', lazy=True)

class OrderItem(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    order_id = db.Column(db.Integer, db.ForeignKey('order.id'), nullable=False)
    product_id = db.Column(db.Integer, db.ForeignKey('product.id'), nullable=False)
    quantity = db.Column(db.Integer, nullable=False)
    unit_price = db.Column(db.Float, nullable=False)  # Price at time of order
    total_price = db.Column(db.Float, nullable=False)  # quantity * unit_price
    notes = db.Column(db.Text)  # Special instructions

class Settings(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    key = db.Column(db.String(50), unique=True, nullable=False)
    value = db.Column(db.Text, nullable=False)
    description = db.Column(db.Text)
    updated_at = db.Column(db.DateTime, nullable=False, default=datetime.now, onupdate=datetime.now)

class SalesReport(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    date = db.Column(db.Date, nullable=False)
    total_sales = db.Column(db.Float, nullable=False, default=0.0)
    total_orders = db.Column(db.Integer, nullable=False, default=0)
    total_items = db.Column(db.Integer, nullable=False, default=0)
    average_order_value = db.Column(db.Float, nullable=False, default=0.0)
    created_at = db.Column(db.DateTime, nullable=False, default=datetime.now)

def init_db():
    # Drop all tables and recreate to fix schema issues
    db.drop_all()
    db.create_all()

    # Create admin user if it doesn't exist
    admin = User.query.filter_by(username='admin').first()
    if not admin:
        admin = User(username='admin', password='admin123', role='admin')
        db.session.add(admin)

    # Create cashier user if it doesn't exist
    cashier = User.query.filter_by(username='cashier').first()
    if not cashier:
        cashier = User(username='cashier', password='cashier123', role='cashier')
        db.session.add(cashier)

    # Add default categories if they don't exist
    if Category.query.count() == 0:
        categories = [
            Category(name='Appetizers', description='Starter dishes and snacks'),
            Category(name='Main Courses', description='Primary dishes and entrees'),
            Category(name='Beverages', description='Drinks and refreshments'),
            Category(name='Desserts', description='Sweet treats and desserts'),
            Category(name='Food', description='General food items'),
            Category(name='Drink', description='General beverages')
        ]
        for category in categories:
            db.session.add(category)
        db.session.commit()  # Commit categories first

    # Add some sample data for testing if products don't exist
    if Product.query.count() == 0:
        # Get category IDs
        food_cat = Category.query.filter_by(name='Food').first()
        drink_cat = Category.query.filter_by(name='Drink').first()
        appetizer_cat = Category.query.filter_by(name='Appetizers').first()
        main_cat = Category.query.filter_by(name='Main Courses').first()
        dessert_cat = Category.query.filter_by(name='Desserts').first()

        # Sample products
        products = [
            Product(name='Burger', description='Juicy beef burger with lettuce and tomato', category='Main Courses', category_id=main_cat.id if main_cat else None, price=9.99),
            Product(name='Pizza', description='Classic margherita pizza', category='Main Courses', category_id=main_cat.id if main_cat else None, price=12.99),
            Product(name='Caesar Salad', description='Fresh romaine lettuce with caesar dressing', category='Appetizers', category_id=appetizer_cat.id if appetizer_cat else None, price=7.99),
            Product(name='French Fries', description='Crispy golden fries', category='Appetizers', category_id=appetizer_cat.id if appetizer_cat else None, price=4.99),
            Product(name='Club Sandwich', description='Triple-decker sandwich with turkey and bacon', category='Main Courses', category_id=main_cat.id if main_cat else None, price=8.99),
            Product(name='Soda', description='Refreshing soft drink', category='Beverages', category_id=drink_cat.id if drink_cat else None, price=2.99),
            Product(name='Coffee', description='Freshly brewed coffee', category='Beverages', category_id=drink_cat.id if drink_cat else None, price=3.99),
            Product(name='Tea', description='Hot herbal tea', category='Beverages', category_id=drink_cat.id if drink_cat else None, price=2.49),
            Product(name='Orange Juice', description='Fresh squeezed orange juice', category='Beverages', category_id=drink_cat.id if drink_cat else None, price=3.49),
            Product(name='Ice Cream', description='Vanilla ice cream with toppings', category='Desserts', category_id=dessert_cat.id if dessert_cat else None, price=4.99),
            Product(name='Chocolate Cake', description='Rich chocolate layer cake', category='Desserts', category_id=dessert_cat.id if dessert_cat else None, price=5.99),
            Product(name='Cookies', description='Homemade chocolate chip cookies', category='Desserts', category_id=dessert_cat.id if dessert_cat else None, price=3.99)
        ]
        for product in products:
            db.session.add(product)

    # Add sample tables if they don't exist
    if Table.query.count() == 0:
        tables = [
            Table(table_number=1, name='Table 1', capacity=4, location='indoor', status='available'),
            Table(table_number=2, name='Table 2', capacity=2, location='indoor', status='available'),
            Table(table_number=3, name='Window Table', capacity=6, location='indoor', status='available'),
            Table(table_number=4, name='Patio Table', capacity=4, location='outdoor', status='available'),
            Table(table_number=5, name='VIP Table', capacity=8, location='private', status='available'),
            Table(table_number=6, name='Bar Counter', capacity=2, location='bar', status='available')
        ]
        for table in tables:
            db.session.add(table)

    # Add default settings if they don't exist
    if Settings.query.count() == 0:
        default_settings = [
            Settings(key='restaurant_name', value='Cash Register Pro Restaurant', description='Restaurant name'),
            Settings(key='restaurant_address', value='123 Main Street\nAnytown, ST 12345', description='Restaurant address'),
            Settings(key='restaurant_phone', value='(*************', description='Restaurant phone number'),
            Settings(key='tax_rate', value='0.0825', description='Tax rate (decimal)'),
            Settings(key='service_charge', value='0.0', description='Service charge rate (decimal)'),
            Settings(key='auto_gratuity', value='false', description='Auto-add gratuity for large parties'),
            Settings(key='auto_print_receipt', value='true', description='Auto-print receipts'),
            Settings(key='print_kitchen_orders', value='true', description='Print kitchen orders')
        ]
        for setting in default_settings:
            db.session.add(setting)

    # Commit all changes
    db.session.commit()
        
