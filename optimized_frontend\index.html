<!DOCTYPE html>
<html lang="en" data-theme="light">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Cash Register Pro - Login</title>
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
  <link rel="stylesheet" href="styles.css">
  <meta name="description" content="Professional Cash Register Application">
</head>
<body>
  <!-- Window Controls -->
  <div class="window-controls">
    <button class="window-control-btn minimize" id="minimize-btn" title="Minimize">−</button>
    <button class="window-control-btn maximize" id="maximize-btn" title="Maximize">□</button>
    <button class="window-control-btn close" id="close-btn" title="Close">×</button>
  </div>

  <!-- Theme Toggle -->
  <button class="theme-toggle" id="theme-toggle" title="Toggle Theme">
    <span class="theme-icon">🌙</span>
  </button>

  <!-- Loading Overlay -->
  <div class="loading-overlay" id="loading-overlay" style="display: none;">
    <div class="loading-spinner"></div>
    <p>Loading...</p>
  </div>

  <!-- Login Container -->
  <div class="login-container">
    <!-- Floating Elements -->
    <div class="floating-elements">
      <div class="floating-element" style="--delay: 0s; --duration: 8s;"></div>
      <div class="floating-element" style="--delay: 2s; --duration: 10s;"></div>
      <div class="floating-element" style="--delay: 4s; --duration: 12s;"></div>
      <div class="floating-element" style="--delay: 6s; --duration: 9s;"></div>
    </div>

    <div class="login-form">
      <!-- Logo and Title -->
      <div class="login-header">
        <div class="logo-container">
          <div class="logo-icon">💰</div>
          <div class="logo-rings">
            <div class="ring ring-1"></div>
            <div class="ring ring-2"></div>
            <div class="ring ring-3"></div>
          </div>
        </div>
        <h1>Cash Register Pro</h1>
        <p class="subtitle">Professional Point of Sale System</p>
      </div>

      <div class="form-group">
        <label for="username">Username</label>
        <div class="input-container">
          <input type="text" id="username" placeholder="Enter your username" autocomplete="username" required>
          <div class="input-focus-border"></div>
        </div>
      </div>

      <div class="form-group">
        <label for="password">Password</label>
        <div class="input-container">
          <input type="password" id="password" placeholder="Enter your password" autocomplete="current-password" required>
          <div class="input-focus-border"></div>
        </div>
      </div>

      <button id="login-btn" type="submit">
        <span class="btn-text">Sign In</span>
        <div class="loading-spinner" style="display: none;"></div>
        <div class="btn-ripple"></div>
      </button>

      <div id="error-message"></div>

      <!-- Signup Link -->
      <div class="auth-switch">
        <p>Don't have an account? <a href="signup.html" class="link">Create Account</a></p>
      </div>

      <!-- Demo Credentials -->
      <div class="demo-credentials">
        <h4>Demo Credentials:</h4>
        <p><strong>Admin:</strong> admin / admin123</p>
        <p><strong>Cashier:</strong> cashier / cashier123</p>
      </div>
    </div>
  </div>

  <!-- Notification Container -->
  <div class="notification-container" id="notification-container"></div>

  <!-- Scripts -->
  <script src="https://unpkg.com/axios/dist/axios.min.js"></script>
  <script src="renderer.js"></script>
</body>
</html>
  
