<!DOCTYPE html>
<html lang="en" data-theme="light">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Cash Register Pro - Admin Dashboard</title>
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
  <link rel="stylesheet" href="styles.css">
  <link rel="stylesheet" href="dashboard.css">
</head>
<body>
  <!-- Window Controls -->
  <div class="window-controls">
    <button class="window-control-btn minimize" id="minimize-btn" title="Minimize">−</button>
    <button class="window-control-btn maximize" id="maximize-btn" title="Maximize">□</button>
    <button class="window-control-btn close" id="close-btn" title="Close">×</button>
  </div>

  <!-- Theme Toggle -->
  <button class="theme-toggle" id="theme-toggle" title="Toggle Theme">
    <span class="theme-icon">🌙</span>
  </button>

  <div class="dashboard-container">
    <!-- Enhanced Header -->
    <header class="dashboard-header">
      <div class="header-left">
        <div class="logo-section">
          <div class="logo-icon">💰</div>
          <div class="header-title">
            <h1>Cash Register Pro</h1>
            <span class="header-subtitle">Admin Dashboard</span>
          </div>
        </div>
      </div>

      <div class="header-right">
        <div class="user-info" id="user-info">
          <div class="user-avatar">👤</div>
          <div class="user-details">
            <span class="user-name" id="user-name">Admin</span>
            <span class="user-role">Administrator</span>
          </div>
        </div>
        <button class="logout-btn" id="logout-btn" title="Logout">
          <span>🚪</span>
          <span>Logout</span>
        </button>
      </div>
    </header>

    <div class="admin-content">
      <!-- Enhanced Sidebar with Extensible Navigation -->
      <div class="admin-sidebar" id="admin-sidebar">
        <div class="sidebar-toggle" id="sidebar-toggle">
          <span class="toggle-icon">☰</span>
        </div>

        <nav class="sidebar-nav">
          <div class="nav-section">
            <h3 class="nav-section-title">Management</h3>
            <button class="nav-btn active" data-section="dashboard">
              <span class="nav-icon">📊</span>
              <span class="nav-text">Dashboard</span>
            </button>
            <button class="nav-btn" data-section="cashiers">
              <span class="nav-icon">👥</span>
              <span class="nav-text">Cashiers</span>
            </button>
            <button class="nav-btn" data-section="tables">
              <span class="nav-icon">🪑</span>
              <span class="nav-text">Tables</span>
            </button>
            <button class="nav-btn" data-section="menu">
              <span class="nav-icon">🍽️</span>
              <span class="nav-text">Menu</span>
            </button>
            <button class="nav-btn" data-section="categories">
              <span class="nav-icon">📂</span>
              <span class="nav-text">Categories</span>
            </button>
          </div>

          <div class="nav-section">
            <h3 class="nav-section-title">Operations</h3>
            <button class="nav-btn" data-section="orders">
              <span class="nav-icon">📋</span>
              <span class="nav-text">Orders</span>
            </button>
            <button class="nav-btn" data-section="sales">
              <span class="nav-icon">💰</span>
              <span class="nav-text">Sales Reports</span>
            </button>
            <button class="nav-btn" data-section="inventory">
              <span class="nav-icon">📦</span>
              <span class="nav-text">Inventory</span>
            </button>
          </div>

          <div class="nav-section">
            <h3 class="nav-section-title">System</h3>
            <button class="nav-btn" data-section="settings">
              <span class="nav-icon">⚙️</span>
              <span class="nav-text">Settings</span>
            </button>
          </div>
        </nav>
      </div>
      
      <div class="admin-main">
        <!-- Dashboard Overview Section -->
        <div class="admin-section active" id="dashboard-section">
          <div class="section-header">
            <h2>📊 Dashboard Overview</h2>
            <p class="section-description">Quick overview of your restaurant operations</p>
          </div>

          <div class="dashboard-stats">
            <div class="stat-card">
              <div class="stat-icon">👥</div>
              <div class="stat-content">
                <h3 id="total-cashiers">0</h3>
                <p>Active Cashiers</p>
              </div>
            </div>
            <div class="stat-card">
              <div class="stat-icon">🪑</div>
              <div class="stat-content">
                <h3 id="total-tables">0</h3>
                <p>Tables</p>
              </div>
            </div>
            <div class="stat-card">
              <div class="stat-icon">🍽️</div>
              <div class="stat-content">
                <h3 id="total-menu-items">0</h3>
                <p>Menu Items</p>
              </div>
            </div>
            <div class="stat-card">
              <div class="stat-icon">💰</div>
              <div class="stat-content">
                <h3 id="today-sales">$0.00</h3>
                <p>Today's Sales</p>
              </div>
            </div>
          </div>

          <div class="dashboard-charts">
            <div class="chart-card">
              <h3>Recent Activity</h3>
              <div id="recent-activity" class="activity-list">
                <!-- Activity items will be loaded here -->
              </div>
            </div>
          </div>
        </div>

        <!-- Enhanced Cashiers Management Section -->
        <div class="admin-section" id="cashiers-section">
          <div class="section-header">
            <h2>👥 Cashier Management</h2>
            <p class="section-description">Manage cashier accounts and permissions</p>
            <button id="add-cashier-btn" class="add-btn">
              <span class="btn-icon">➕</span>
              <span>Add New Cashier</span>
            </button>
          </div>

          <div class="table-container">
            <table id="cashiers-table" class="data-table">
              <thead>
                <tr>
                  <th>ID</th>
                  <th>Username</th>
                  <th>Role</th>
                  <th>Status</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                <!-- Cashier data will be loaded here -->
              </tbody>
            </table>
          </div>

          <!-- Cashier Modal -->
          <div class="modal" id="cashier-modal">
            <div class="modal-content">
              <div class="modal-header">
                <h3 id="cashier-modal-title">Add New Cashier</h3>
                <span class="close-btn">&times;</span>
              </div>
              <form id="cashier-form" class="modal-form">
                <div class="form-group">
                  <label for="cashier-username">Username</label>
                  <input type="text" id="cashier-username" placeholder="Enter username" required>
                  <small class="field-hint">Must be unique and at least 3 characters</small>
                </div>
                <div class="form-group">
                  <label for="cashier-password">Password</label>
                  <input type="password" id="cashier-password" placeholder="Enter password" required>
                  <small class="field-hint">Must be at least 6 characters</small>
                </div>
                <div class="form-group">
                  <label for="cashier-role">Role</label>
                  <select id="cashier-role" required>
                    <option value="cashier">Cashier</option>
                    <option value="admin">Administrator</option>
                  </select>
                  <small class="field-hint">Choose the appropriate access level</small>
                </div>
                <div class="form-actions">
                  <button type="button" class="btn-secondary" onclick="closeCashierModal()">Cancel</button>
                  <button type="submit" class="btn-primary">Save Cashier</button>
                </div>
              </form>
            </div>
          </div>
        </div>
        
        <!-- Enhanced Tables Management Section -->
        <div class="admin-section" id="tables-section">
          <div class="section-header">
            <h2>🪑 Table Management</h2>
            <p class="section-description">Manage restaurant tables and seating arrangements</p>
            <button id="add-table-btn" class="add-btn">
              <span class="btn-icon">➕</span>
              <span>Add New Table</span>
            </button>
          </div>

          <div class="table-container">
            <table id="tables-table" class="data-table">
              <thead>
                <tr>
                  <th>Table Number</th>
                  <th>Name</th>
                  <th>Capacity</th>
                  <th>Status</th>
                  <th>Location</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                <!-- Table data will be loaded here -->
              </tbody>
            </table>
          </div>

          <!-- Table Modal -->
          <div class="modal" id="table-modal">
            <div class="modal-content">
              <div class="modal-header">
                <h3 id="table-modal-title">Add New Table</h3>
                <span class="close-btn">&times;</span>
              </div>
              <form id="table-form" class="modal-form">
                <div class="form-group">
                  <label for="table-number">Table Number</label>
                  <input type="number" id="table-number" placeholder="Enter table number" required min="1">
                  <small class="field-hint">Unique table identifier</small>
                </div>
                <div class="form-group">
                  <label for="table-name">Table Name</label>
                  <input type="text" id="table-name" placeholder="e.g., Window Table, VIP Table" required>
                  <small class="field-hint">Descriptive name for the table</small>
                </div>
                <div class="form-group">
                  <label for="table-capacity">Capacity</label>
                  <input type="number" id="table-capacity" placeholder="Number of seats" required min="1" max="20">
                  <small class="field-hint">Maximum number of guests</small>
                </div>
                <div class="form-group">
                  <label for="table-location">Location</label>
                  <select id="table-location" required>
                    <option value="">Select location</option>
                    <option value="indoor">Indoor</option>
                    <option value="outdoor">Outdoor</option>
                    <option value="patio">Patio</option>
                    <option value="private">Private Room</option>
                    <option value="bar">Bar Area</option>
                  </select>
                  <small class="field-hint">Table location in restaurant</small>
                </div>
                <div class="form-group">
                  <label for="table-status">Status</label>
                  <select id="table-status" required>
                    <option value="available">Available</option>
                    <option value="occupied">Occupied</option>
                    <option value="reserved">Reserved</option>
                    <option value="maintenance">Under Maintenance</option>
                  </select>
                  <small class="field-hint">Current table status</small>
                </div>
                <div class="form-actions">
                  <button type="button" class="btn-secondary" onclick="closeTableModal()">Cancel</button>
                  <button type="submit" class="btn-primary">Save Table</button>
                </div>
              </form>
            </div>
          </div>
        </div>

        <!-- Enhanced Menu Management Section -->
        <div class="admin-section" id="menu-section">
          <div class="section-header">
            <h2>🍽️ Menu Management</h2>
            <p class="section-description">Manage menu items, prices, and availability</p>
            <button id="add-menu-item-btn" class="add-btn">
              <span class="btn-icon">➕</span>
              <span>Add Menu Item</span>
            </button>
          </div>

          <div class="table-container">
            <table id="menu-table" class="data-table">
              <thead>
                <tr>
                  <th>ID</th>
                  <th>Name</th>
                  <th>Category</th>
                  <th>Price</th>
                  <th>Available</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                <!-- Menu items will be loaded here -->
              </tbody>
            </table>
          </div>

          <!-- Menu Item Modal -->
          <div class="modal" id="menu-modal">
            <div class="modal-content">
              <div class="modal-header">
                <h3 id="menu-modal-title">Add Menu Item</h3>
                <span class="close-btn">&times;</span>
              </div>
              <form id="menu-form" class="modal-form">
                <div class="form-group">
                  <label for="menu-name">Item Name</label>
                  <input type="text" id="menu-name" placeholder="Enter item name" required>
                  <small class="field-hint">Name of the menu item</small>
                </div>
                <div class="form-group">
                  <label for="menu-description">Description</label>
                  <textarea id="menu-description" placeholder="Describe the item" rows="3"></textarea>
                  <small class="field-hint">Brief description of the item</small>
                </div>
                <div class="form-group">
                  <label for="menu-category">Category</label>
                  <select id="menu-category" required>
                    <option value="">Select category</option>
                    <!-- Categories will be loaded dynamically -->
                  </select>
                  <small class="field-hint">Item category</small>
                </div>
                <div class="form-group">
                  <label for="menu-price">Price ($)</label>
                  <input type="number" id="menu-price" placeholder="0.00" step="0.01" min="0" required>
                  <small class="field-hint">Price in dollars</small>
                </div>
                <div class="form-group">
                  <label class="checkbox-label">
                    <input type="checkbox" id="menu-available" checked>
                    <span class="checkmark"></span>
                    Available for ordering
                  </label>
                </div>
                <div class="form-actions">
                  <button type="button" class="btn-secondary" onclick="closeMenuModal()">Cancel</button>
                  <button type="submit" class="btn-primary">Save Item</button>
                </div>
              </form>
            </div>
          </div>
        </div>

        <!-- Categories Management Section -->
        <div class="admin-section" id="categories-section">
          <div class="section-header">
            <h2>📂 Category Management</h2>
            <p class="section-description">Manage menu categories and organization</p>
            <button id="add-category-btn" class="add-btn">
              <span class="btn-icon">➕</span>
              <span>Add Category</span>
            </button>
          </div>

          <div class="table-container">
            <table id="categories-table" class="data-table">
              <thead>
                <tr>
                  <th>ID</th>
                  <th>Name</th>
                  <th>Description</th>
                  <th>Items Count</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                <!-- Categories will be loaded here -->
              </tbody>
            </table>
          </div>

          <!-- Category Modal -->
          <div class="modal" id="category-modal">
            <div class="modal-content">
              <div class="modal-header">
                <h3 id="category-modal-title">Add Category</h3>
                <span class="close-btn">&times;</span>
              </div>
              <form id="category-form" class="modal-form">
                <div class="form-group">
                  <label for="category-name">Category Name</label>
                  <input type="text" id="category-name" placeholder="Enter category name" required>
                  <small class="field-hint">Name of the category</small>
                </div>
                <div class="form-group">
                  <label for="category-description">Description</label>
                  <textarea id="category-description" placeholder="Describe the category" rows="3"></textarea>
                  <small class="field-hint">Brief description of the category</small>
                </div>
                <div class="form-actions">
                  <button type="button" class="btn-secondary" onclick="closeCategoryModal()">Cancel</button>
                  <button type="submit" class="btn-primary">Save Category</button>
                </div>
              </form>
            </div>
          </div>
        </div>

        <!-- Orders Section -->
        <div class="admin-section" id="orders-section">
          <h2>Orders Management</h2>
          <div class="admin-table-container">
            <table id="orders-table">
              <thead>
                <tr>
                  <th>ID</th>
                  <th>Table</th>
                  <th>Total</th>
                  <th>Status</th>
                  <th>Date</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody></tbody>
            </table>
          </div>
          
          <div class="modal" id="order-modal">
            <div class="modal-content">
              <span class="close-btn">&times;</span>
              <h3>Order Details</h3>
              <div id="order-details"></div>
              <div class="form-group">
                <label for="order-status">Status</label>
                <select id="order-status">
                  <option value="pending">Pending</option>
                  <option value="completed">Completed</option>
                  <option value="cancelled">Cancelled</option>
                </select>
              </div>
              <button id="update-order-btn">Update Status</button>
              <button id="admin-print-receipt-btn">Print Receipt</button>
            </div>
          </div>
        </div>
        
        <!-- Sales Section -->
        <div class="admin-section" id="sales-section">
          <h2>Sales Reports</h2>
          <div class="sales-filters">
            <div class="form-group">
              <label for="sales-date">Date</label>
              <input type="date" id="sales-date">
            </div>
            <button id="view-daily-sales-btn">View Daily Sales</button>
            
            <div class="form-group">
              <label for="sales-start-date">Start Date</label>
              <input type="date" id="sales-start-date">
            </div>
            <div class="form-group">
              <label for="sales-end-date">End Date</label>
              <input type="date" id="sales-end-date">
            </div>
            <button id="view-range-sales-btn">View Range</button>
          </div>
          
          <div id="sales-report"></div>
        </div>
        
        <!-- Settings Section -->
        <div class="admin-section" id="settings-section">
          <div class="section-header">
            <h2>⚙️ System Settings</h2>
            <p class="section-description">Configure system preferences and settings</p>
          </div>

          <div class="settings-grid">
            <div class="setting-card">
              <h3>🏪 Restaurant Information</h3>
              <form id="restaurant-form">
                <div class="form-group">
                  <label for="restaurant-name">Restaurant Name</label>
                  <input type="text" id="restaurant-name" placeholder="Your Restaurant Name">
                </div>
                <div class="form-group">
                  <label for="restaurant-address">Address</label>
                  <textarea id="restaurant-address" placeholder="Restaurant Address" rows="3"></textarea>
                </div>
                <div class="form-group">
                  <label for="restaurant-phone">Phone</label>
                  <input type="tel" id="restaurant-phone" placeholder="(*************">
                </div>
                <button type="submit" class="btn-primary">Save Information</button>
              </form>
            </div>

            <div class="setting-card">
              <h3>💳 Payment Settings</h3>
              <form id="payment-form">
                <div class="form-group">
                  <label for="tax-rate">Tax Rate (%)</label>
                  <input type="number" id="tax-rate" placeholder="8.25" step="0.01" min="0" max="100">
                </div>
                <div class="form-group">
                  <label for="service-charge">Service Charge (%)</label>
                  <input type="number" id="service-charge" placeholder="0" step="0.01" min="0" max="100">
                </div>
                <div class="form-group">
                  <label class="checkbox-label">
                    <input type="checkbox" id="auto-gratuity">
                    <span class="checkmark"></span>
                    Auto-add gratuity for large parties
                  </label>
                </div>
                <button type="submit" class="btn-primary">Save Settings</button>
              </form>
            </div>

            <div class="setting-card">
              <h3>🖨️ Printer Settings</h3>
              <form id="printer-form">
                <div class="form-group">
                  <label for="receipt-printer">Receipt Printer</label>
                  <select id="receipt-printer">
                    <option value="default">Default Printer</option>
                    <option value="thermal">Thermal Printer</option>
                  </select>
                </div>
                <div class="form-group">
                  <label class="checkbox-label">
                    <input type="checkbox" id="auto-print-receipt">
                    <span class="checkmark"></span>
                    Auto-print receipts
                  </label>
                </div>
                <div class="form-group">
                  <label class="checkbox-label">
                    <input type="checkbox" id="print-kitchen-orders">
                    <span class="checkmark"></span>
                    Print kitchen orders
                  </label>
                </div>
                <button type="submit" class="btn-primary">Save Settings</button>
              </form>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  
  <script src="https://unpkg.com/axios/dist/axios.min.js"></script>
  <script src="admin.js"></script>
</body>
</html>

