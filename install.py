#!/usr/bin/env python3
"""
Cash Register Pro - Complete Windows Installer
Creates a full installation package for Windows
"""

import os
import sys
import subprocess
import shutil
import zipfile
import json
from pathlib import Path

class CashRegisterInstaller:
    def __init__(self):
        self.root_dir = Path(__file__).parent
        self.install_dir = Path("C:/Program Files/Cash Register Pro")
        self.desktop_dir = Path.home() / "Desktop"
        self.start_menu_dir = Path.home() / "AppData/Roaming/Microsoft/Windows/Start Menu/Programs"
        
    def print_header(self):
        print("=" * 80)
        print("💰 CASH REGISTER PRO - WINDOWS INSTALLER 💰")
        print("=" * 80)
        print("Creating complete installation package for Windows...")
        print()
        
    def check_admin_rights(self):
        """Check if running with admin privileges"""
        try:
            import ctypes
            return ctypes.windll.shell32.IsUserAnAdmin()
        except:
            return False
    
    def install_dependencies(self):
        print("📦 Installing Python dependencies...")
        
        dependencies = [
            'Flask==3.0.0',
            'Flask-SQLAlchemy==3.0.5',
            'Flask-CORS==4.0.0',
            'PyJWT==2.8.0',
            'Werkzeug==3.0.1',
            'python-dotenv==1.0.0'
        ]
        
        for dep in dependencies:
            try:
                subprocess.run([sys.executable, '-m', 'pip', 'install', dep], 
                             check=True, capture_output=True)
                print(f"✅ {dep}")
            except subprocess.CalledProcessError:
                print(f"❌ Failed to install {dep}")
                return False
        
        return True
    
    def create_optimized_backend(self):
        print("🔧 Creating optimized backend...")
        
        # Create optimized app.py
        optimized_app = '''
from flask import Flask, request, jsonify
from flask_cors import CORS
from flask_sqlalchemy import SQLAlchemy
from werkzeug.security import generate_password_hash, check_password_hash
import jwt
import os
from datetime import datetime, timedelta
from functools import wraps

# Initialize Flask app
app = Flask(__name__)
app.config['SECRET_KEY'] = 'cash_register_secret_key_2024'
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///cash_register.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

# Initialize extensions
db = SQLAlchemy(app)
CORS(app)

# Database Models
class User(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    password_hash = db.Column(db.String(120), nullable=False)
    role = db.Column(db.String(20), nullable=False, default='cashier')
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    def set_password(self, password):
        self.password_hash = generate_password_hash(password)
    
    def check_password(self, password):
        return check_password_hash(self.password_hash, password)

class Category(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    description = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

class Product(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    description = db.Column(db.Text)
    price = db.Column(db.Float, nullable=False)
    category_id = db.Column(db.Integer, db.ForeignKey('category.id'))
    available = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

class Table(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    number = db.Column(db.Integer, unique=True, nullable=False)
    name = db.Column(db.String(100), nullable=False)
    capacity = db.Column(db.Integer, nullable=False)
    location = db.Column(db.String(50))
    status = db.Column(db.String(20), default='available')
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

class Order(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    table_id = db.Column(db.Integer, db.ForeignKey('table.id'))
    subtotal = db.Column(db.Float, nullable=False)
    tax_amount = db.Column(db.Float, nullable=False)
    total = db.Column(db.Float, nullable=False)
    status = db.Column(db.String(20), default='pending')
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

class OrderItem(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    order_id = db.Column(db.Integer, db.ForeignKey('order.id'))
    product_id = db.Column(db.Integer, db.ForeignKey('product.id'))
    quantity = db.Column(db.Integer, nullable=False)
    unit_price = db.Column(db.Float, nullable=False)
    total_price = db.Column(db.Float, nullable=False)

# Authentication decorator
def token_required(f):
    @wraps(f)
    def decorated(*args, **kwargs):
        token = request.headers.get('Authorization')
        if not token:
            return jsonify({'error': 'Token is missing'}), 401
        try:
            if token.startswith('Bearer '):
                token = token[7:]
            data = jwt.decode(token, app.config['SECRET_KEY'], algorithms=['HS256'])
            current_user = User.query.get(data['user_id'])
        except:
            return jsonify({'error': 'Token is invalid'}), 401
        return f(current_user, *args, **kwargs)
    return decorated

# API Routes
@app.route('/')
def home():
    return {'message': 'Cash Register Pro API', 'status': 'running'}

@app.route('/api/auth/login', methods=['POST'])
def login():
    data = request.get_json()
    user = User.query.filter_by(username=data.get('username')).first()
    
    if user and user.check_password(data.get('password')):
        token = jwt.encode({
            'user_id': user.id,
            'role': user.role,
            'exp': datetime.utcnow() + timedelta(hours=24)
        }, app.config['SECRET_KEY'])
        
        return jsonify({
            'token': token,
            'user': {'id': user.id, 'username': user.username, 'role': user.role}
        })
    
    return jsonify({'error': 'Invalid credentials'}), 401

@app.route('/api/auth/verify', methods=['GET'])
@token_required
def verify_token(current_user):
    return jsonify({'user': {'id': current_user.id, 'username': current_user.username, 'role': current_user.role}})

@app.route('/api/categories', methods=['GET', 'POST'])
def handle_categories():
    if request.method == 'GET':
        categories = Category.query.all()
        return jsonify([{'id': c.id, 'name': c.name, 'description': c.description} for c in categories])
    
    elif request.method == 'POST':
        data = request.get_json()
        category = Category(name=data['name'], description=data.get('description', ''))
        db.session.add(category)
        db.session.commit()
        return jsonify({'id': category.id, 'name': category.name}), 201

@app.route('/api/products', methods=['GET', 'POST'])
def handle_products():
    if request.method == 'GET':
        products = Product.query.all()
        return jsonify([{
            'id': p.id, 'name': p.name, 'description': p.description,
            'price': p.price, 'category_id': p.category_id, 'available': p.available
        } for p in products])
    
    elif request.method == 'POST':
        data = request.get_json()
        product = Product(
            name=data['name'],
            description=data.get('description', ''),
            price=float(data['price']),
            category_id=data.get('category_id'),
            available=data.get('available', True)
        )
        db.session.add(product)
        db.session.commit()
        return jsonify({'id': product.id, 'name': product.name}), 201

@app.route('/api/tables', methods=['GET', 'POST'])
def handle_tables():
    if request.method == 'GET':
        tables = Table.query.all()
        return jsonify([{
            'id': t.id, 'number': t.number, 'name': t.name,
            'capacity': t.capacity, 'location': t.location, 'status': t.status
        } for t in tables])
    
    elif request.method == 'POST':
        data = request.get_json()
        table = Table(
            number=data['number'],
            name=data['name'],
            capacity=data['capacity'],
            location=data.get('location', 'indoor')
        )
        db.session.add(table)
        db.session.commit()
        return jsonify({'id': table.id, 'number': table.number}), 201

@app.route('/api/tables/<int:table_id>', methods=['PUT'])
def update_table(table_id):
    table = Table.query.get_or_404(table_id)
    data = request.get_json()
    table.status = data.get('status', table.status)
    db.session.commit()
    return jsonify({'id': table.id, 'status': table.status})

@app.route('/api/orders', methods=['GET', 'POST'])
def handle_orders():
    if request.method == 'GET':
        orders = Order.query.all()
        return jsonify([{
            'id': o.id, 'table_id': o.table_id, 'subtotal': o.subtotal,
            'tax_amount': o.tax_amount, 'total': o.total, 'status': o.status,
            'created_at': o.created_at.isoformat()
        } for o in orders])
    
    elif request.method == 'POST':
        data = request.get_json()
        order = Order(
            table_id=data['table_id'],
            subtotal=data['subtotal'],
            tax_amount=data['tax_amount'],
            total=data['total'],
            status=data.get('status', 'pending')
        )
        db.session.add(order)
        db.session.commit()
        
        # Add order items
        for item in data.get('items', []):
            order_item = OrderItem(
                order_id=order.id,
                product_id=item['product_id'],
                quantity=item['quantity'],
                unit_price=item['unit_price'],
                total_price=item['total_price']
            )
            db.session.add(order_item)
        
        db.session.commit()
        return jsonify({'id': order.id, 'status': order.status}), 201

@app.route('/api/sales/daily', methods=['GET'])
def daily_sales():
    today = datetime.utcnow().date()
    orders = Order.query.filter(
        db.func.date(Order.created_at) == today,
        Order.status == 'completed'
    ).all()
    
    total_sales = sum(order.total for order in orders)
    return jsonify({
        'date': today.isoformat(),
        'total_sales': total_sales,
        'order_count': len(orders)
    })

def init_db():
    """Initialize database with sample data"""
    db.create_all()
    
    # Create admin user
    if not User.query.filter_by(username='admin').first():
        admin = User(username='admin', role='admin')
        admin.set_password('admin123')
        db.session.add(admin)
    
    # Create cashier user
    if not User.query.filter_by(username='cashier').first():
        cashier = User(username='cashier', role='cashier')
        cashier.set_password('cashier123')
        db.session.add(cashier)
    
    # Create sample categories
    if Category.query.count() == 0:
        categories = [
            Category(name='Appetizers', description='Starters and small plates'),
            Category(name='Main Courses', description='Main dishes and entrees'),
            Category(name='Beverages', description='Drinks and beverages'),
            Category(name='Desserts', description='Sweet treats and desserts')
        ]
        for cat in categories:
            db.session.add(cat)
    
    # Create sample products
    if Product.query.count() == 0:
        products = [
            Product(name='Caesar Salad', description='Fresh romaine lettuce with caesar dressing', price=12.99, category_id=1),
            Product(name='Grilled Chicken', description='Herb-seasoned grilled chicken breast', price=18.99, category_id=2),
            Product(name='Coca Cola', description='Classic soft drink', price=2.99, category_id=3),
            Product(name='Chocolate Cake', description='Rich chocolate layer cake', price=6.99, category_id=4)
        ]
        for prod in products:
            db.session.add(prod)
    
    # Create sample tables
    if Table.query.count() == 0:
        tables = [
            Table(number=1, name='Window Table 1', capacity=4, location='indoor'),
            Table(number=2, name='Corner Table', capacity=2, location='indoor'),
            Table(number=3, name='Patio Table 1', capacity=6, location='outdoor'),
            Table(number=4, name='Bar Table', capacity=2, location='bar')
        ]
        for table in tables:
            db.session.add(table)
    
    db.session.commit()
    print("Database initialized with sample data")

if __name__ == '__main__':
    print("Starting Cash Register Pro Backend...")
    init_db()
    print("Server running on http://localhost:5000")
    app.run(debug=False, host='0.0.0.0', port=5000, use_reloader=False)
'''
        
        # Write optimized backend
        backend_dir = self.root_dir / "optimized_backend"
        backend_dir.mkdir(exist_ok=True)
        
        with open(backend_dir / "app.py", 'w', encoding='utf-8') as f:
            f.write(optimized_app)
        
        print("✅ Optimized backend created")
        return True
    
    def create_optimized_frontend(self):
        print("🎨 Creating optimized frontend...")
        
        frontend_dir = self.root_dir / "optimized_frontend"
        frontend_dir.mkdir(exist_ok=True)
        
        # Copy and optimize only necessary frontend files
        essential_files = [
            "index.html",
            "main-dashboard.html", 
            "admin-dashboard.html",
            "main-dashboard.js",
            "admin.js",
            "renderer.js",
            "main.js",
            "styles.css",
            "dashboard.css",
            "main-dashboard.css",
            "package.json"
        ]
        
        for file in essential_files:
            src = self.root_dir / "frontend" / file
            if src.exists():
                shutil.copy2(src, frontend_dir / file)
        
        # Copy assets
        assets_src = self.root_dir / "frontend" / "assets"
        assets_dest = frontend_dir / "assets"
        if assets_src.exists():
            shutil.copytree(assets_src, assets_dest, dirs_exist_ok=True)
        
        print("✅ Optimized frontend created")
        return True
    
    def create_installer_package(self):
        print("📦 Creating installation package...")
        
        package_dir = self.root_dir / "CashRegisterPro_Installation"
        package_dir.mkdir(exist_ok=True)
        
        # Copy optimized files
        shutil.copytree(self.root_dir / "optimized_backend", package_dir / "backend", dirs_exist_ok=True)
        shutil.copytree(self.root_dir / "optimized_frontend", package_dir / "frontend", dirs_exist_ok=True)
        
        # Create installer script
        installer_script = f'''
@echo off
title Cash Register Pro - Installer
echo ========================================
echo    Cash Register Pro - Installation
echo ========================================
echo.

echo Installing Cash Register Pro...
echo.

REM Create installation directory
if not exist "C:\\Program Files\\Cash Register Pro" (
    mkdir "C:\\Program Files\\Cash Register Pro"
)

REM Copy files
echo Copying application files...
xcopy /s /e /i "%~dp0backend" "C:\\Program Files\\Cash Register Pro\\backend\\"
xcopy /s /e /i "%~dp0frontend" "C:\\Program Files\\Cash Register Pro\\frontend\\"
copy "%~dp0launcher.py" "C:\\Program Files\\Cash Register Pro\\"
copy "%~dp0README.txt" "C:\\Program Files\\Cash Register Pro\\"

REM Install Python dependencies
echo Installing dependencies...
pip install Flask Flask-SQLAlchemy Flask-CORS PyJWT Werkzeug python-dotenv

REM Create desktop shortcut
echo Creating desktop shortcut...
powershell "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%USERPROFILE%\\Desktop\\Cash Register Pro.lnk'); $Shortcut.TargetPath = 'python'; $Shortcut.Arguments = 'C:\\Program Files\\Cash Register Pro\\launcher.py'; $Shortcut.WorkingDirectory = 'C:\\Program Files\\Cash Register Pro'; $Shortcut.Save()"

REM Create start menu shortcut
echo Creating start menu shortcut...
if not exist "%APPDATA%\\Microsoft\\Windows\\Start Menu\\Programs\\Cash Register Pro" (
    mkdir "%APPDATA%\\Microsoft\\Windows\\Start Menu\\Programs\\Cash Register Pro"
)
powershell "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%APPDATA%\\Microsoft\\Windows\\Start Menu\\Programs\\Cash Register Pro\\Cash Register Pro.lnk'); $Shortcut.TargetPath = 'python'; $Shortcut.Arguments = 'C:\\Program Files\\Cash Register Pro\\launcher.py'; $Shortcut.WorkingDirectory = 'C:\\Program Files\\Cash Register Pro'; $Shortcut.Save()"

echo.
echo ========================================
echo Installation completed successfully!
echo.
echo You can now start Cash Register Pro from:
echo - Desktop shortcut
echo - Start Menu
echo.
echo Default login credentials:
echo   Admin: admin / admin123
echo   Cashier: cashier / cashier123
echo ========================================
echo.
pause
'''
        
        with open(package_dir / "install.bat", 'w', encoding='utf-8') as f:
            f.write(installer_script)
        
        print("✅ Installation package created")
        return True
    
    def create_launcher(self):
        print("🚀 Creating application launcher...")
        
        launcher_script = '''
import os
import sys
import subprocess
import time
import webbrowser
from pathlib import Path

def main():
    print("=" * 60)
    print("💰 CASH REGISTER PRO 💰")
    print("=" * 60)
    print()
    
    # Get installation directory
    install_dir = Path("C:/Program Files/Cash Register Pro")
    if not install_dir.exists():
        install_dir = Path(__file__).parent
    
    backend_dir = install_dir / "backend"
    frontend_dir = install_dir / "frontend"
    
    print("🚀 Starting Cash Register Pro...")
    
    # Start backend
    backend_process = subprocess.Popen([
        sys.executable, "app.py"
    ], cwd=backend_dir)
    
    print("⏳ Waiting for backend to start...")
    time.sleep(3)
    
    # Check if Node.js is available for frontend
    try:
        subprocess.run(['node', '--version'], capture_output=True, check=True)
        print("🖥️  Starting Electron frontend...")
        subprocess.Popen(['npm', 'start'], cwd=frontend_dir)
    except:
        print("🌐 Opening web interface...")
        time.sleep(2)
        webbrowser.open('http://localhost:5000')
    
    print("\\n" + "=" * 60)
    print("🎉 CASH REGISTER PRO IS NOW RUNNING! 🎉")
    print("=" * 60)
    print()
    print("📍 Backend: http://localhost:5000")
    print("🖥️  Frontend: Desktop Application")
    print()
    print("🔐 Login Credentials:")
    print("   Admin: admin / admin123")
    print("   Cashier: cashier / cashier123")
    print()
    print("=" * 60)
    
    try:
        backend_process.wait()
    except KeyboardInterrupt:
        print("\\n🔄 Shutting down...")
        backend_process.terminate()

if __name__ == "__main__":
    main()
'''
        
        package_dir = self.root_dir / "CashRegisterPro_Installation"
        with open(package_dir / "launcher.py", 'w', encoding='utf-8') as f:
            f.write(launcher_script)
        
        print("✅ Launcher created")
        return True
    
    def create_documentation(self):
        print("📝 Creating documentation...")
        
        readme_content = '''# Cash Register Pro - Professional POS System

## Installation Instructions

1. Run `install.bat` as Administrator
2. Wait for installation to complete
3. Use desktop shortcut or start menu to launch

## Default Credentials

- Admin: username=admin, password=admin123
- Cashier: username=cashier, password=cashier123

## Features

✅ Complete Point-of-Sale System
✅ Table Management with Visual Cards
✅ Menu and Category Management
✅ Order Processing with Real-time Calculations
✅ Admin Dashboard for System Management
✅ Real-time Clock Display
✅ Professional UI/UX Design
✅ Local SQLite Database
✅ Timestamps on Everything

## System Requirements

- Windows 10 or later
- Python 3.7 or later
- 4GB RAM minimum
- 1GB free disk space

## Support

For technical support, please contact your system administrator.

## Version

Cash Register Pro v2.0 - Professional Edition
'''
        
        package_dir = self.root_dir / "CashRegisterPro_Installation"
        with open(package_dir / "README.txt", 'w', encoding='utf-8') as f:
            f.write(readme_content)
        
        print("✅ Documentation created")
        return True
    
    def create_zip_package(self):
        print("📦 Creating ZIP installation package...")
        
        package_dir = self.root_dir / "CashRegisterPro_Installation"
        zip_path = self.root_dir / "CashRegisterPro_Windows_Installer.zip"
        
        with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
            for root, dirs, files in os.walk(package_dir):
                for file in files:
                    file_path = Path(root) / file
                    arc_path = file_path.relative_to(package_dir)
                    zipf.write(file_path, arc_path)
        
        print(f"✅ ZIP package created: {zip_path}")
        return True
    
    def run(self):
        try:
            self.print_header()
            
            if not self.install_dependencies():
                return False
            
            if not self.create_optimized_backend():
                return False
            
            if not self.create_optimized_frontend():
                return False
            
            if not self.create_installer_package():
                return False
            
            if not self.create_launcher():
                return False
            
            if not self.create_documentation():
                return False
            
            if not self.create_zip_package():
                return False
            
            print("\n" + "=" * 80)
            print("🎉 INSTALLATION PACKAGE CREATED SUCCESSFULLY! 🎉")
            print("=" * 80)
            print()
            print("📦 Package Location: CashRegisterPro_Windows_Installer.zip")
            print("📁 Installation Folder: CashRegisterPro_Installation/")
            print()
            print("🚀 To Install:")
            print("   1. Extract the ZIP file")
            print("   2. Run install.bat as Administrator")
            print("   3. Use desktop shortcut to launch")
            print()
            print("✨ Features Included:")
            print("   ✅ Optimized code with minimal dependencies")
            print("   ✅ Complete Windows installation package")
            print("   ✅ Desktop and Start Menu shortcuts")
            print("   ✅ Professional POS system")
            print("   ✅ Real-time clock and timestamps")
            print("   ✅ All requested features working")
            print()
            print("=" * 80)
            
            return True
            
        except Exception as e:
            print(f"❌ Error: {e}")
            return False

def main():
    installer = CashRegisterInstaller()
    success = installer.run()
    
    if not success:
        print("❌ Installation package creation failed!")
        input("Press Enter to exit...")
        sys.exit(1)
    
    input("Press Enter to exit...")
    sys.exit(0)

if __name__ == "__main__":
    main()
