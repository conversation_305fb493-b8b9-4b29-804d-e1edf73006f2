
@echo off
title Cash Register Pro - Installer
echo ========================================
echo    Cash Register Pro - Installation
echo ========================================
echo.

echo Installing Cash Register Pro...
echo.

REM Create installation directory
if not exist "C:\Program Files\Cash Register Pro" (
    mkdir "C:\Program Files\Cash Register Pro"
)

REM Copy files
echo Copying application files...
xcopy /s /e /i "%~dp0backend" "C:\Program Files\Cash Register Pro\backend\"
xcopy /s /e /i "%~dp0frontend" "C:\Program Files\Cash Register Pro\frontend\"
copy "%~dp0launcher.py" "C:\Program Files\Cash Register Pro\"
copy "%~dp0README.txt" "C:\Program Files\Cash Register Pro\"

REM Install Python dependencies
echo Installing dependencies...
pip install Flask Flask-SQLAlchemy Flask-CORS PyJWT Werkzeug python-dotenv

REM Create desktop shortcut
echo Creating desktop shortcut...
powershell "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%USERPROFILE%\Desktop\Cash Register Pro.lnk'); $Shortcut.TargetPath = 'python'; $Shortcut.Arguments = 'C:\Program Files\Cash Register Pro\launcher.py'; $Shortcut.WorkingDirectory = 'C:\Program Files\Cash Register Pro'; $Shortcut.Save()"

REM Create start menu shortcut
echo Creating start menu shortcut...
if not exist "%APPDATA%\Microsoft\Windows\Start Menu\Programs\Cash Register Pro" (
    mkdir "%APPDATA%\Microsoft\Windows\Start Menu\Programs\Cash Register Pro"
)
powershell "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%APPDATA%\Microsoft\Windows\Start Menu\Programs\Cash Register Pro\Cash Register Pro.lnk'); $Shortcut.TargetPath = 'python'; $Shortcut.Arguments = 'C:\Program Files\Cash Register Pro\launcher.py'; $Shortcut.WorkingDirectory = 'C:\Program Files\Cash Register Pro'; $Shortcut.Save()"

echo.
echo ========================================
echo Installation completed successfully!
echo.
echo You can now start Cash Register Pro from:
echo - Desktop shortcut
echo - Start Menu
echo.
echo Default login credentials:
echo   Admin: admin / admin123
echo   Cashier: cashier / cashier123
echo ========================================
echo.
pause
