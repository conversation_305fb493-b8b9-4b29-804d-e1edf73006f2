#!/usr/bin/env python3
"""
Cash Register Pro - Final Working Launcher
Launches the complete optimized application
"""

import os
import sys
import subprocess
import time
import webbrowser
from pathlib import Path

def main():
    print("=" * 70)
    print("💰 CASH REGISTER PRO - PROFESSIONAL POS SYSTEM 💰")
    print("=" * 70)
    print()
    print("🚀 Starting optimized application...")
    print()
    
    # Use the optimized installation
    install_dir = Path("CashRegisterPro_Installation")
    if not install_dir.exists():
        print("❌ Installation directory not found!")
        print("Please run 'python install.py' first to create the installation.")
        input("Press Enter to exit...")
        return
    
    backend_dir = install_dir / "backend"
    frontend_dir = install_dir / "frontend"
    
    # Clean any existing database to ensure fresh start
    db_path = backend_dir / "cash_register.db"
    if db_path.exists():
        try:
            db_path.unlink()
            print("🗄️  Database reset for fresh start")
        except:
            pass
    
    print("🔧 Starting backend server...")
    
    # Start backend
    backend_process = subprocess.Popen([
        sys.executable, "app.py"
    ], cwd=backend_dir, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
    
    print("⏳ Waiting for backend to initialize...")
    time.sleep(4)
    
    # Check if Node.js is available for Electron frontend
    try:
        result = subprocess.run(['node', '--version'], 
                              capture_output=True, check=True)
        print("🖥️  Starting Electron frontend...")
        
        # Install frontend dependencies if needed
        package_json = frontend_dir / "package.json"
        node_modules = frontend_dir / "node_modules"
        
        if package_json.exists() and not node_modules.exists():
            print("📦 Installing frontend dependencies...")
            subprocess.run(['npm', 'install'], cwd=frontend_dir, 
                         capture_output=True)
        
        # Start Electron app
        frontend_process = subprocess.Popen(['npm', 'start'], 
                                          cwd=frontend_dir,
                                          stdout=subprocess.PIPE, 
                                          stderr=subprocess.PIPE)
        
        print("✅ Electron application starting...")
        
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("⚠️  Node.js not found, opening web interface...")
        time.sleep(2)
        webbrowser.open('http://localhost:5000')
        frontend_process = None
    
    print()
    print("=" * 70)
    print("🎉 CASH REGISTER PRO IS NOW RUNNING! 🎉")
    print("=" * 70)
    print()
    print("📍 Backend API: http://localhost:5000")
    print("🖥️  Frontend: Desktop Application / Web Interface")
    print()
    print("🔐 DEFAULT LOGIN CREDENTIALS:")
    print("   👑 Admin: username=admin, password=admin123")
    print("   👤 Cashier: username=cashier, password=cashier123")
    print()
    print("✨ FEATURES AVAILABLE:")
    print("   ✅ Complete Point-of-Sale System")
    print("   ✅ Table Management with Visual Cards")
    print("   ✅ Menu and Category Management")
    print("   ✅ Order Processing with Real-time Calculations")
    print("   ✅ Admin Dashboard for System Management")
    print("   ✅ Real-time Clock Display (in header)")
    print("   ✅ Professional UI/UX Design")
    print("   ✅ Local SQLite Database")
    print("   ✅ Timestamps on All Records")
    print()
    print("🎯 QUICK START GUIDE:")
    print("   1. Login with credentials above")
    print("   2. Admin: Manage users, tables, menu items")
    print("   3. Cashier: Select tables, take orders, process payments")
    print("   4. Real-time clock visible in dashboard header")
    print("   5. All data saved locally in SQLite database")
    print()
    print("⚠️  Keep this window open while using the application")
    print("🔄 Close the application window or press Ctrl+C to stop")
    print("=" * 70)
    
    try:
        # Keep the application running
        if frontend_process:
            frontend_process.wait()
        else:
            # If no frontend process, wait for user input
            input("\\nPress Enter to stop the application...")
    except KeyboardInterrupt:
        print("\\n🔄 Shutting down...")
    finally:
        # Clean up processes
        print("🧹 Cleaning up...")
        
        if backend_process:
            try:
                backend_process.terminate()
                backend_process.wait(timeout=5)
            except:
                try:
                    backend_process.kill()
                except:
                    pass
        
        if 'frontend_process' in locals() and frontend_process:
            try:
                frontend_process.terminate()
                frontend_process.wait(timeout=5)
            except:
                try:
                    frontend_process.kill()
                except:
                    pass
        
        print("✅ Application stopped successfully")
        print("👋 Thank you for using Cash Register Pro!")

if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        print(f"❌ Error: {e}")
        input("Press Enter to exit...")
    
    input("\\nPress Enter to exit...")
