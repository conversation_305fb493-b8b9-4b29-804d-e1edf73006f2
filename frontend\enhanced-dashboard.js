// Enhanced Dashboard JavaScript - Complete POS System

class EnhancedCashRegisterDashboard {
  constructor() {
    this.currentUser = null;
    this.currentPage = 'dashboard';
    this.selectedTable = null;
    this.currentOrder = [];
    this.categories = [];
    this.menuItems = [];
    this.tables = [];
    this.orders = [];
    this.TAX_RATE = 0.08;
    
    this.init();
  }

  async init() {
    console.log('Initializing Enhanced Cash Register Dashboard...');
    
    // Load user data from localStorage
    this.loadUserData();
    
    // Setup event listeners
    this.setupEventListeners();
    
    // Update time and date
    this.updateDateTime();
    setInterval(() => this.updateDateTime(), 1000);
    
    // Load initial data
    await this.loadInitialData();
    
    // Setup touch and keyboard support
    this.setupAccessibility();
    
    console.log('Enhanced Dashboard initialized successfully');
  }

  loadUserData() {
    const token = localStorage.getItem('authToken');
    const username = localStorage.getItem('username');
    const userRole = localStorage.getItem('userRole');
    
    if (!token || !username) {
      window.location.href = 'index.html';
      return;
    }

    this.currentUser = { username, role: userRole, token };
    
    const userNameEl = document.getElementById('user-name');
    const userRoleEl = document.getElementById('user-role');
    
    if (userNameEl) userNameEl.textContent = username;
    if (userRoleEl) userRoleEl.textContent = userRole || 'Staff';
  }

  setupEventListeners() {
    // Sidebar toggle
    const sidebarToggle = document.getElementById('sidebar-toggle');
    if (sidebarToggle) {
      sidebarToggle.addEventListener('click', () => {
        this.toggleSidebar();
      });
    }

    // Navigation items
    document.querySelectorAll('.nav-item').forEach(item => {
      item.addEventListener('click', (e) => {
        e.preventDefault();
        const page = item.dataset.page;
        if (page) {
          this.navigateToPage(page);
        }
      });
    });

    // Logout button
    const logoutBtn = document.getElementById('logout-btn');
    if (logoutBtn) {
      logoutBtn.addEventListener('click', () => {
        this.logout();
      });
    }

    // Quick action buttons
    const quickNewOrder = document.getElementById('quick-new-order');
    if (quickNewOrder) {
      quickNewOrder.addEventListener('click', () => {
        this.navigateToPage('take-order');
      });
    }

    // Dashboard quick actions
    document.querySelectorAll('.action-card').forEach(card => {
      card.addEventListener('click', () => {
        const action = card.dataset.action;
        this.handleQuickAction(action);
      });
    });

    // Order controls
    const clearOrderBtn = document.getElementById('clear-order');
    if (clearOrderBtn) {
      clearOrderBtn.addEventListener('click', () => {
        this.clearOrder();
      });
    }

    const processOrderBtn = document.getElementById('process-order');
    if (processOrderBtn) {
      processOrderBtn.addEventListener('click', () => {
        this.processOrder();
      });
    }

    // Add category button
    const addCategoryBtn = document.getElementById('add-category-btn');
    if (addCategoryBtn) {
      addCategoryBtn.addEventListener('click', () => {
        this.showAddCategoryModal();
      });
    }

    // Add table button
    const addTableBtn = document.getElementById('add-table-btn');
    if (addTableBtn) {
      addTableBtn.addEventListener('click', () => {
        this.showAddTableModal();
      });
    }
  }

  updateDateTime() {
    const now = new Date();
    const timeString = now.toLocaleTimeString('en-US', {
      hour12: false,
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
    const dateString = now.toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });

    const timeEl = document.getElementById('current-time');
    const dateEl = document.getElementById('current-date');
    
    if (timeEl) timeEl.textContent = timeString;
    if (dateEl) dateEl.textContent = dateString;
  }

  toggleSidebar() {
    const sidebar = document.getElementById('sidebar');
    const mainContent = document.getElementById('main-content');
    
    if (sidebar) sidebar.classList.toggle('collapsed');
    if (mainContent) mainContent.classList.toggle('expanded');
  }

  navigateToPage(page) {
    // Update active navigation
    document.querySelectorAll('.nav-item').forEach(item => {
      item.classList.remove('active');
    });
    
    const activeNavItem = document.querySelector(`[data-page="${page}"]`);
    if (activeNavItem) {
      activeNavItem.classList.add('active');
    }

    // Show page
    document.querySelectorAll('.page').forEach(p => {
      p.classList.remove('active');
    });
    
    const targetPage = document.getElementById(`${page}-page`);
    if (targetPage) {
      targetPage.classList.add('active');
    }

    this.currentPage = page;

    // Load page-specific data
    this.loadPageData(page);
  }

  async loadPageData(page) {
    switch (page) {
      case 'dashboard':
        await this.loadDashboardData();
        break;
      case 'take-order':
        await this.loadOrderPageData();
        break;
      case 'menu':
        await this.loadMenuManagementData();
        break;
      case 'tables':
        await this.loadTableManagementData();
        break;
    }
  }

  async loadInitialData() {
    try {
      // Load categories
      await this.loadCategories();
      
      // Load menu items
      await this.loadMenuItems();
      
      // Load tables
      await this.loadTables();
      
      // Load dashboard data
      await this.loadDashboardData();
      
    } catch (error) {
      console.error('Error loading initial data:', error);
      this.showNotification('Error loading data', 'error');
    }
  }

  async loadCategories() {
    try {
      // For now, use default categories
      this.categories = [
        { id: 1, name: 'Cold Drinks', icon: '🥤' },
        { id: 2, name: 'Hot Drinks', icon: '☕' },
        { id: 3, name: 'Food', icon: '🍔' },
        { id: 4, name: 'Desserts', icon: '🍰' },
        { id: 5, name: 'Snacks', icon: '🍿' }
      ];
    } catch (error) {
      console.error('Error loading categories:', error);
    }
  }

  async loadMenuItems() {
    try {
      // For now, use default menu items
      this.menuItems = [
        // Cold Drinks
        { id: 1, categoryId: 1, name: 'Coca Cola', price: 2.50, description: 'Classic cola drink' },
        { id: 2, categoryId: 1, name: 'Orange Juice', price: 3.00, description: 'Fresh orange juice' },
        { id: 3, categoryId: 1, name: 'Water', price: 1.50, description: 'Bottled water' },
        { id: 4, categoryId: 1, name: 'Sprite', price: 2.50, description: 'Lemon-lime soda' },
        { id: 5, categoryId: 1, name: 'Iced Tea', price: 2.75, description: 'Refreshing iced tea' },
        
        // Hot Drinks
        { id: 6, categoryId: 2, name: 'Coffee', price: 2.00, description: 'Fresh brewed coffee' },
        { id: 7, categoryId: 2, name: 'Tea', price: 1.80, description: 'Hot tea' },
        { id: 8, categoryId: 2, name: 'Hot Chocolate', price: 3.50, description: 'Rich hot chocolate' },
        { id: 9, categoryId: 2, name: 'Cappuccino', price: 4.00, description: 'Italian cappuccino' },
        { id: 10, categoryId: 2, name: 'Latte', price: 4.50, description: 'Creamy latte' },
        
        // Food
        { id: 11, categoryId: 3, name: 'Burger', price: 8.50, description: 'Classic beef burger' },
        { id: 12, categoryId: 3, name: 'Pizza Slice', price: 4.00, description: 'Cheese pizza slice' },
        { id: 13, categoryId: 3, name: 'Sandwich', price: 6.00, description: 'Club sandwich' },
        { id: 14, categoryId: 3, name: 'Pasta', price: 12.00, description: 'Italian pasta' },
        { id: 15, categoryId: 3, name: 'Salad', price: 7.50, description: 'Fresh garden salad' },
        
        // Desserts
        { id: 16, categoryId: 4, name: 'Ice Cream', price: 4.50, description: 'Vanilla ice cream' },
        { id: 17, categoryId: 4, name: 'Cake Slice', price: 5.00, description: 'Chocolate cake' },
        { id: 18, categoryId: 4, name: 'Cookies', price: 3.00, description: 'Chocolate chip cookies' },
        { id: 19, categoryId: 4, name: 'Pie', price: 4.75, description: 'Apple pie slice' },
        
        // Snacks
        { id: 20, categoryId: 5, name: 'Chips', price: 2.00, description: 'Potato chips' },
        { id: 21, categoryId: 5, name: 'Nuts', price: 3.50, description: 'Mixed nuts' },
        { id: 22, categoryId: 5, name: 'Pretzels', price: 2.25, description: 'Salted pretzels' },
        { id: 23, categoryId: 5, name: 'Popcorn', price: 3.00, description: 'Buttered popcorn' }
      ];
    } catch (error) {
      console.error('Error loading menu items:', error);
    }
  }

  async loadTables() {
    try {
      // Generate default tables
      this.tables = [];
      for (let i = 1; i <= 20; i++) {
        this.tables.push({
          id: i,
          number: i,
          status: Math.random() > 0.8 ? 'occupied' : 'available', // Some random occupied tables
          capacity: Math.floor(Math.random() * 6) + 2, // 2-8 people
          currentOrder: null
        });
      }
    } catch (error) {
      console.error('Error loading tables:', error);
    }
  }

  async loadDashboardData() {
    try {
      // Update stats (mock data for now)
      const todaySalesEl = document.getElementById('today-sales');
      const todayOrdersEl = document.getElementById('today-orders');
      const todayCustomersEl = document.getElementById('today-customers');
      const activeTablesEl = document.getElementById('active-tables');
      
      if (todaySalesEl) todaySalesEl.textContent = '$1,234.56';
      if (todayOrdersEl) todayOrdersEl.textContent = '45';
      if (todayCustomersEl) todayCustomersEl.textContent = '38';
      if (activeTablesEl) {
        activeTablesEl.textContent = this.tables.filter(t => t.status === 'occupied').length.toString();
      }
      
      // Load recent orders
      this.loadRecentOrders();
      
    } catch (error) {
      console.error('Error loading dashboard data:', error);
    }
  }

  loadRecentOrders() {
    const recentOrdersContainer = document.getElementById('recent-orders');
    if (!recentOrdersContainer) return;
    
    // Mock recent orders
    const recentOrders = [
      { id: 1, table: 5, total: 25.50, time: '2 minutes ago', status: 'completed' },
      { id: 2, table: 3, total: 18.75, time: '5 minutes ago', status: 'preparing' },
      { id: 3, table: 8, total: 42.00, time: '8 minutes ago', status: 'served' },
      { id: 4, table: 12, total: 15.25, time: '12 minutes ago', status: 'completed' },
      { id: 5, table: 7, total: 33.80, time: '15 minutes ago', status: 'completed' }
    ];

    recentOrdersContainer.innerHTML = recentOrders.map(order => `
      <div class="recent-order-item">
        <div class="order-info">
          <span class="order-id">#${order.id}</span>
          <span class="order-table">Table ${order.table}</span>
          <span class="order-total">$${order.total.toFixed(2)}</span>
        </div>
        <div class="order-meta">
          <span class="order-time">${order.time}</span>
          <span class="order-status status-${order.status}">${order.status}</span>
        </div>
      </div>
    `).join('');
  }

  async loadOrderPageData() {
    this.renderTables();
    this.renderCategories();
    this.renderMenuItems();
    this.updateOrderSummary();
  }

  renderTables() {
    const tableGrid = document.getElementById('table-grid');
    if (!tableGrid) return;
    
    tableGrid.innerHTML = this.tables.map(table => `
      <div class="table-item ${table.status} ${this.selectedTable?.id === table.id ? 'selected' : ''}" 
           data-table-id="${table.id}">
        <div class="table-number">${table.number}</div>
        <div class="table-status">${table.status}</div>
      </div>
    `).join('');

    // Add click listeners
    tableGrid.querySelectorAll('.table-item').forEach(item => {
      item.addEventListener('click', () => {
        const tableId = parseInt(item.dataset.tableId);
        this.selectTable(tableId);
      });
    });
  }

  renderCategories() {
    const categoryTabs = document.getElementById('category-tabs');
    if (!categoryTabs) return;
    
    categoryTabs.innerHTML = this.categories.map((category, index) => `
      <div class="category-tab ${index === 0 ? 'active' : ''}" data-category-id="${category.id}">
        ${category.icon} ${category.name}
      </div>
    `).join('');

    // Add click listeners
    categoryTabs.querySelectorAll('.category-tab').forEach(tab => {
      tab.addEventListener('click', () => {
        const categoryId = parseInt(tab.dataset.categoryId);
        this.selectCategory(categoryId);
      });
    });
  }

  renderMenuItems(categoryId = 1) {
    const menuItemsContainer = document.getElementById('menu-items');
    if (!menuItemsContainer) return;
    
    const filteredItems = this.menuItems.filter(item => item.categoryId === categoryId);
    
    menuItemsContainer.innerHTML = filteredItems.map(item => `
      <div class="menu-item" data-item-id="${item.id}">
        <div class="menu-item-name">${item.name}</div>
        <div class="menu-item-price">$${item.price.toFixed(2)}</div>
        <div class="menu-item-description">${item.description}</div>
      </div>
    `).join('');

    // Add click listeners
    menuItemsContainer.querySelectorAll('.menu-item').forEach(item => {
      item.addEventListener('click', () => {
        const itemId = parseInt(item.dataset.itemId);
        this.addToOrder(itemId);
      });
    });
  }

  selectTable(tableId) {
    this.selectedTable = this.tables.find(t => t.id === tableId);
    this.renderTables();
    this.updateSelectedTable();
  }

  selectCategory(categoryId) {
    // Update active tab
    document.querySelectorAll('.category-tab').forEach(tab => {
      tab.classList.remove('active');
    });
    
    const activeTab = document.querySelector(`[data-category-id="${categoryId}"]`);
    if (activeTab) {
      activeTab.classList.add('active');
    }

    // Render items for this category
    this.renderMenuItems(categoryId);
  }

  addToOrder(itemId) {
    if (!this.selectedTable) {
      this.showNotification('Please select a table first', 'warning');
      return;
    }

    const menuItem = this.menuItems.find(item => item.id === itemId);
    if (!menuItem) return;
    
    const existingItem = this.currentOrder.find(item => item.id === itemId);

    if (existingItem) {
      existingItem.quantity += 1;
    } else {
      this.currentOrder.push({
        ...menuItem,
        quantity: 1
      });
    }

    this.updateOrderSummary();
    this.showNotification(`Added ${menuItem.name} to order`, 'success');
  }

  updateSelectedTable() {
    const selectedTableElement = document.getElementById('selected-table');
    if (!selectedTableElement) return;
    
    if (this.selectedTable) {
      selectedTableElement.innerHTML = `
        <i class="fas fa-table"></i>
        Table ${this.selectedTable.number} (${this.selectedTable.capacity} seats)
      `;
    } else {
      selectedTableElement.innerHTML = 'No table selected';
    }
  }

  updateOrderSummary() {
    const orderItemsContainer = document.getElementById('order-items');
    const subtotalElement = document.getElementById('order-subtotal');
    const taxElement = document.getElementById('order-tax');
    const totalElement = document.getElementById('order-total');

    if (!orderItemsContainer) return;

    // Render order items
    orderItemsContainer.innerHTML = this.currentOrder.map(item => `
      <div class="order-item">
        <div class="order-item-info">
          <div class="order-item-name">${item.name}</div>
          <div class="order-item-price">$${item.price.toFixed(2)} each</div>
        </div>
        <div class="order-item-controls">
          <button class="quantity-btn" onclick="enhancedDashboard.changeQuantity(${item.id}, -1)">
            <i class="fas fa-minus"></i>
          </button>
          <span class="quantity-display">${item.quantity}</span>
          <button class="quantity-btn" onclick="enhancedDashboard.changeQuantity(${item.id}, 1)">
            <i class="fas fa-plus"></i>
          </button>
        </div>
      </div>
    `).join('');

    // Calculate totals
    const subtotal = this.currentOrder.reduce((sum, item) => sum + (item.price * item.quantity), 0);
    const tax = subtotal * this.TAX_RATE;
    const total = subtotal + tax;

    if (subtotalElement) subtotalElement.textContent = `$${subtotal.toFixed(2)}`;
    if (taxElement) taxElement.textContent = `$${tax.toFixed(2)}`;
    if (totalElement) totalElement.textContent = `$${total.toFixed(2)}`;
  }

  changeQuantity(itemId, change) {
    const item = this.currentOrder.find(item => item.id === itemId);
    if (item) {
      item.quantity += change;
      if (item.quantity <= 0) {
        this.currentOrder = this.currentOrder.filter(i => i.id !== itemId);
      }
      this.updateOrderSummary();
    }
  }

  clearOrder() {
    this.currentOrder = [];
    this.selectedTable = null;
    this.updateOrderSummary();
    this.updateSelectedTable();
    this.renderTables();
    this.showNotification('Order cleared', 'info');
  }

  async processOrder() {
    if (!this.selectedTable) {
      this.showNotification('Please select a table', 'error');
      return;
    }

    if (this.currentOrder.length === 0) {
      this.showNotification('Please add items to the order', 'error');
      return;
    }

    try {
      // Process the order (send to backend)
      const subtotal = this.currentOrder.reduce((sum, item) => sum + (item.price * item.quantity), 0);
      const tax = subtotal * this.TAX_RATE;
      const total = subtotal + tax;
      
      const orderData = {
        tableId: this.selectedTable.id,
        items: this.currentOrder,
        subtotal: subtotal,
        tax: tax,
        total: total,
        timestamp: new Date().toISOString()
      };

      // For now, just simulate success
      console.log('Processing order:', orderData);
      
      // Update table status
      this.selectedTable.status = 'occupied';
      this.selectedTable.currentOrder = orderData;

      this.showNotification('Order processed successfully!', 'success');
      this.clearOrder();
      
    } catch (error) {
      console.error('Error processing order:', error);
      this.showNotification('Error processing order', 'error');
    }
  }

  handleQuickAction(action) {
    switch (action) {
      case 'new-order':
        this.navigateToPage('take-order');
        break;
      case 'view-menu':
        this.navigateToPage('menu');
        break;
      case 'manage-tables':
        this.navigateToPage('tables');
        break;
      case 'daily-report':
        this.navigateToPage('reports');
        break;
    }
  }

  setupAccessibility() {
    // Keyboard navigation
    document.addEventListener('keydown', (e) => {
      if (e.ctrlKey) {
        switch (e.key) {
          case '1':
            e.preventDefault();
            this.navigateToPage('dashboard');
            break;
          case '2':
            e.preventDefault();
            this.navigateToPage('take-order');
            break;
          case '3':
            e.preventDefault();
            this.navigateToPage('menu');
            break;
          case '4':
            e.preventDefault();
            this.navigateToPage('tables');
            break;
        }
      }
    });

    // Touch support for mobile devices
    let touchStartY = 0;
    document.addEventListener('touchstart', (e) => {
      touchStartY = e.touches[0].clientY;
    });

    document.addEventListener('touchend', (e) => {
      const touchEndY = e.changedTouches[0].clientY;
      const diff = touchStartY - touchEndY;
      
      // Swipe gestures for navigation
      if (Math.abs(diff) > 50) {
        if (diff > 0) {
          // Swipe up - could trigger some action
        } else {
          // Swipe down - could trigger some action
        }
      }
    });
  }

  showNotification(message, type = 'info') {
    // Create notification if container exists
    const container = document.getElementById('notification-container');
    if (!container) {
      console.log(`Notification: ${message} (${type})`);
      return;
    }
    
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
      <i class="fas fa-${this.getNotificationIcon(type)}"></i>
      <span>${message}</span>
      <button class="notification-close">&times;</button>
    `;

    container.appendChild(notification);

    // Auto remove after 5 seconds
    setTimeout(() => {
      if (notification.parentNode) {
        notification.remove();
      }
    }, 5000);

    // Close button
    const closeBtn = notification.querySelector('.notification-close');
    if (closeBtn) {
      closeBtn.addEventListener('click', () => {
        notification.remove();
      });
    }
  }

  getNotificationIcon(type) {
    switch (type) {
      case 'success': return 'check-circle';
      case 'error': return 'exclamation-circle';
      case 'warning': return 'exclamation-triangle';
      default: return 'info-circle';
    }
  }

  showAddCategoryModal() {
    this.showModal('add-category-modal');
  }

  showAddTableModal() {
    this.showModal('add-table-modal');
  }

  showModal(modalId) {
    const modal = document.getElementById(modalId);
    const overlay = document.getElementById('modal-overlay');

    if (modal && overlay) {
      overlay.classList.add('active');
      modal.classList.add('active');

      // Setup close handlers
      this.setupModalCloseHandlers(modalId);
    }
  }

  hideModal(modalId) {
    const modal = document.getElementById(modalId);
    const overlay = document.getElementById('modal-overlay');

    if (modal && overlay) {
      modal.classList.remove('active');
      overlay.classList.remove('active');
    }
  }

  setupModalCloseHandlers(modalId) {
    // Close on overlay click
    const overlay = document.getElementById('modal-overlay');
    overlay.addEventListener('click', (e) => {
      if (e.target === overlay) {
        this.hideModal(modalId);
      }
    });

    // Close on close button click
    document.querySelectorAll(`[data-modal="${modalId}"]`).forEach(btn => {
      btn.addEventListener('click', () => {
        this.hideModal(modalId);
      });
    });

    // Close on escape key
    document.addEventListener('keydown', (e) => {
      if (e.key === 'Escape') {
        this.hideModal(modalId);
      }
    });
  }

  async loadMenuManagementData() {
    this.renderCategoriesManagement();
    this.renderMenuItemsManagement();
    this.setupMenuManagementEvents();
  }

  renderCategoriesManagement() {
    const categoriesGrid = document.getElementById('categories-grid');
    if (!categoriesGrid) return;

    categoriesGrid.innerHTML = this.categories.map(category => {
      const itemCount = this.menuItems.filter(item => item.categoryId === category.id).length;
      return `
        <div class="category-card" data-category-id="${category.id}">
          <div class="category-actions">
            <button class="action-btn edit-btn" onclick="enhancedDashboard.editCategory(${category.id})">
              <i class="fas fa-edit"></i>
            </button>
            <button class="action-btn delete-btn" onclick="enhancedDashboard.deleteCategory(${category.id})">
              <i class="fas fa-trash"></i>
            </button>
          </div>
          <span class="category-icon">${category.icon}</span>
          <div class="category-name">${category.name}</div>
          <div class="category-count">${itemCount} items</div>
        </div>
      `;
    }).join('');
  }

  renderMenuItemsManagement() {
    const menuItemsGrid = document.getElementById('menu-items-grid');
    const categoryFilter = document.getElementById('category-filter');

    if (!menuItemsGrid) return;

    // Update category filter
    if (categoryFilter) {
      categoryFilter.innerHTML = `
        <option value="all">All Categories</option>
        ${this.categories.map(cat => `<option value="${cat.id}">${cat.name}</option>`).join('')}
      `;
    }

    // Get filtered items
    const selectedCategory = categoryFilter?.value || 'all';
    const searchTerm = document.getElementById('item-search')?.value.toLowerCase() || '';

    let filteredItems = this.menuItems;

    if (selectedCategory !== 'all') {
      filteredItems = filteredItems.filter(item => item.categoryId === parseInt(selectedCategory));
    }

    if (searchTerm) {
      filteredItems = filteredItems.filter(item =>
        item.name.toLowerCase().includes(searchTerm) ||
        item.description.toLowerCase().includes(searchTerm)
      );
    }

    menuItemsGrid.innerHTML = filteredItems.map(item => {
      const category = this.categories.find(cat => cat.id === item.categoryId);
      return `
        <div class="menu-item-card" data-item-id="${item.id}">
          <div class="item-category-badge">${category?.icon} ${category?.name}</div>
          <div class="category-actions">
            <button class="action-btn edit-btn" onclick="enhancedDashboard.editMenuItem(${item.id})">
              <i class="fas fa-edit"></i>
            </button>
            <button class="action-btn delete-btn" onclick="enhancedDashboard.deleteMenuItem(${item.id})">
              <i class="fas fa-trash"></i>
            </button>
          </div>
          <div class="item-name">${item.name}</div>
          <div class="item-price">$${item.price.toFixed(2)}</div>
          <div class="item-description">${item.description}</div>
        </div>
      `;
    }).join('');
  }

  setupMenuManagementEvents() {
    // Add item button
    const addItemBtn = document.getElementById('add-item-btn');
    if (addItemBtn) {
      addItemBtn.addEventListener('click', () => {
        this.showAddItemModal();
      });
    }

    // Category filter
    const categoryFilter = document.getElementById('category-filter');
    if (categoryFilter) {
      categoryFilter.addEventListener('change', () => {
        this.renderMenuItemsManagement();
      });
    }

    // Search filter
    const itemSearch = document.getElementById('item-search');
    if (itemSearch) {
      itemSearch.addEventListener('input', () => {
        this.renderMenuItemsManagement();
      });
    }

    // Add category form
    const addCategoryForm = document.getElementById('add-category-form');
    if (addCategoryForm) {
      addCategoryForm.addEventListener('submit', (e) => {
        e.preventDefault();
        this.handleAddCategory();
      });
    }

    // Add item form
    const addItemForm = document.getElementById('add-item-form');
    if (addItemForm) {
      addItemForm.addEventListener('submit', (e) => {
        e.preventDefault();
        this.handleAddMenuItem();
      });
    }
  }

  showAddItemModal() {
    // Populate category dropdown
    const categorySelect = document.getElementById('item-category');
    if (categorySelect) {
      categorySelect.innerHTML = this.categories.map(cat =>
        `<option value="${cat.id}">${cat.icon} ${cat.name}</option>`
      ).join('');
    }

    this.showModal('add-item-modal');
  }

  handleAddCategory() {
    const nameInput = document.getElementById('category-name');
    const iconInput = document.getElementById('category-icon');

    if (!nameInput || !iconInput) return;

    const name = nameInput.value.trim();
    const icon = iconInput.value.trim() || '📁';

    if (!name) {
      this.showNotification('Please enter a category name', 'error');
      return;
    }

    // Add new category
    const newCategory = {
      id: Math.max(...this.categories.map(c => c.id)) + 1,
      name: name,
      icon: icon
    };

    this.categories.push(newCategory);
    this.renderCategoriesManagement();
    this.renderMenuItemsManagement();

    // Clear form
    nameInput.value = '';
    iconInput.value = '';

    this.hideModal('add-category-modal');
    this.showNotification(`Category "${name}" added successfully!`, 'success');
  }

  handleAddMenuItem() {
    const nameInput = document.getElementById('item-name');
    const categorySelect = document.getElementById('item-category');
    const priceInput = document.getElementById('item-price');
    const descriptionInput = document.getElementById('item-description');

    if (!nameInput || !categorySelect || !priceInput) return;

    const name = nameInput.value.trim();
    const categoryId = parseInt(categorySelect.value);
    const price = parseFloat(priceInput.value);
    const description = descriptionInput?.value.trim() || '';

    if (!name || !categoryId || !price) {
      this.showNotification('Please fill in all required fields', 'error');
      return;
    }

    // Add new menu item
    const newItem = {
      id: Math.max(...this.menuItems.map(i => i.id)) + 1,
      categoryId: categoryId,
      name: name,
      price: price,
      description: description
    };

    this.menuItems.push(newItem);
    this.renderMenuItemsManagement();

    // Clear form
    nameInput.value = '';
    priceInput.value = '';
    if (descriptionInput) descriptionInput.value = '';

    this.hideModal('add-item-modal');
    this.showNotification(`Menu item "${name}" added successfully!`, 'success');
  }

  editCategory(categoryId) {
    const category = this.categories.find(c => c.id === categoryId);
    if (!category) return;

    const newName = prompt('Enter new category name:', category.name);
    const newIcon = prompt('Enter new category icon:', category.icon);

    if (newName && newName.trim()) {
      category.name = newName.trim();
      if (newIcon && newIcon.trim()) {
        category.icon = newIcon.trim();
      }
      this.renderCategoriesManagement();
      this.renderMenuItemsManagement();
      this.showNotification(`Category updated successfully!`, 'success');
    }
  }

  deleteCategory(categoryId) {
    const category = this.categories.find(c => c.id === categoryId);
    if (!category) return;

    const itemCount = this.menuItems.filter(item => item.categoryId === categoryId).length;

    if (itemCount > 0) {
      this.showNotification(`Cannot delete category with ${itemCount} items. Remove items first.`, 'error');
      return;
    }

    if (confirm(`Are you sure you want to delete the category "${category.name}"?`)) {
      this.categories = this.categories.filter(c => c.id !== categoryId);
      this.renderCategoriesManagement();
      this.renderMenuItemsManagement();
      this.showNotification(`Category "${category.name}" deleted successfully!`, 'success');
    }
  }

  editMenuItem(itemId) {
    const item = this.menuItems.find(i => i.id === itemId);
    if (!item) return;

    const newName = prompt('Enter new item name:', item.name);
    const newPrice = prompt('Enter new price:', item.price);
    const newDescription = prompt('Enter new description:', item.description);

    if (newName && newName.trim() && newPrice && !isNaN(parseFloat(newPrice))) {
      item.name = newName.trim();
      item.price = parseFloat(newPrice);
      if (newDescription !== null) {
        item.description = newDescription.trim();
      }
      this.renderMenuItemsManagement();
      this.showNotification(`Menu item updated successfully!`, 'success');
    }
  }

  deleteMenuItem(itemId) {
    const item = this.menuItems.find(i => i.id === itemId);
    if (!item) return;

    if (confirm(`Are you sure you want to delete "${item.name}"?`)) {
      this.menuItems = this.menuItems.filter(i => i.id !== itemId);
      this.renderMenuItemsManagement();
      this.showNotification(`Menu item "${item.name}" deleted successfully!`, 'success');
    }
  }

  async loadTableManagementData() {
    this.renderTableManagement();
    this.updateTableStats();
    this.setupTableManagementEvents();
  }

  renderTableManagement() {
    const tablesGrid = document.getElementById('tables-management-grid');
    if (!tablesGrid) return;

    tablesGrid.innerHTML = this.tables.map(table => `
      <div class="table-management-item ${table.status}" data-table-id="${table.id}">
        <div class="table-management-actions">
          <button class="action-btn edit-btn" onclick="enhancedDashboard.editTable(${table.id})">
            <i class="fas fa-edit"></i>
          </button>
          <button class="action-btn delete-btn" onclick="enhancedDashboard.deleteTable(${table.id})">
            <i class="fas fa-trash"></i>
          </button>
        </div>
        <div class="table-management-number">${table.number}</div>
        <div class="table-management-capacity">${table.capacity} seats</div>
        <div class="table-management-status">${table.status}</div>
      </div>
    `).join('');
  }

  updateTableStats() {
    const totalTables = this.tables.length;
    const availableTables = this.tables.filter(t => t.status === 'available').length;
    const occupiedTables = this.tables.filter(t => t.status === 'occupied').length;

    const totalEl = document.getElementById('total-tables');
    const availableEl = document.getElementById('available-tables');
    const occupiedEl = document.getElementById('occupied-tables');

    if (totalEl) totalEl.textContent = totalTables;
    if (availableEl) availableEl.textContent = availableTables;
    if (occupiedEl) occupiedEl.textContent = occupiedTables;
  }

  setupTableManagementEvents() {
    // Add table form
    const addTableForm = document.getElementById('add-table-form');
    if (addTableForm) {
      addTableForm.addEventListener('submit', (e) => {
        e.preventDefault();
        this.handleAddTable();
      });
    }

    // Clear all tables button
    const clearAllBtn = document.getElementById('clear-all-tables-btn');
    if (clearAllBtn) {
      clearAllBtn.addEventListener('click', () => {
        this.clearAllTables();
      });
    }
  }

  handleAddTable() {
    const numberInput = document.getElementById('table-number');
    const capacityInput = document.getElementById('table-capacity');

    if (!numberInput || !capacityInput) return;

    const number = parseInt(numberInput.value);
    const capacity = parseInt(capacityInput.value);

    if (!number || !capacity) {
      this.showNotification('Please fill in all fields', 'error');
      return;
    }

    // Check if table number already exists
    if (this.tables.find(t => t.number === number)) {
      this.showNotification(`Table ${number} already exists`, 'error');
      return;
    }

    // Add new table
    const newTable = {
      id: Math.max(...this.tables.map(t => t.id)) + 1,
      number: number,
      capacity: capacity,
      status: 'available',
      currentOrder: null
    };

    this.tables.push(newTable);
    this.renderTableManagement();
    this.updateTableStats();

    // Clear form
    numberInput.value = '';
    capacityInput.value = '4';

    this.hideModal('add-table-modal');
    this.showNotification(`Table ${number} added successfully!`, 'success');
  }

  editTable(tableId) {
    const table = this.tables.find(t => t.id === tableId);
    if (!table) return;

    const newNumber = prompt('Enter new table number:', table.number);
    const newCapacity = prompt('Enter new capacity:', table.capacity);

    if (newNumber && !isNaN(parseInt(newNumber)) && newCapacity && !isNaN(parseInt(newCapacity))) {
      const number = parseInt(newNumber);
      const capacity = parseInt(newCapacity);

      // Check if new number conflicts with existing table
      const existingTable = this.tables.find(t => t.number === number && t.id !== tableId);
      if (existingTable) {
        this.showNotification(`Table ${number} already exists`, 'error');
        return;
      }

      table.number = number;
      table.capacity = capacity;
      this.renderTableManagement();
      this.showNotification(`Table updated successfully!`, 'success');
    }
  }

  deleteTable(tableId) {
    const table = this.tables.find(t => t.id === tableId);
    if (!table) return;

    if (table.status === 'occupied') {
      this.showNotification('Cannot delete occupied table', 'error');
      return;
    }

    if (confirm(`Are you sure you want to delete Table ${table.number}?`)) {
      this.tables = this.tables.filter(t => t.id !== tableId);
      this.renderTableManagement();
      this.updateTableStats();
      this.showNotification(`Table ${table.number} deleted successfully!`, 'success');
    }
  }

  clearAllTables() {
    const occupiedTables = this.tables.filter(t => t.status === 'occupied').length;

    if (occupiedTables > 0) {
      if (!confirm(`There are ${occupiedTables} occupied tables. Clear all tables anyway?`)) {
        return;
      }
    }

    if (confirm('Are you sure you want to clear all tables? This will set all tables to available.')) {
      this.tables.forEach(table => {
        table.status = 'available';
        table.currentOrder = null;
      });

      this.renderTableManagement();
      this.updateTableStats();
      this.showNotification('All tables cleared successfully!', 'success');
    }
  }

  logout() {
    localStorage.removeItem('currentUser');
    localStorage.removeItem('authToken');
    localStorage.removeItem('userRole');
    localStorage.removeItem('username');
    window.location.href = 'index.html';
  }
}

// Initialize enhanced dashboard when DOM is loaded
let enhancedDashboard;
document.addEventListener('DOMContentLoaded', () => {
  enhancedDashboard = new EnhancedCashRegisterDashboard();
});

// Make dashboard globally accessible for inline event handlers
window.enhancedDashboard = enhancedDashboard;
